<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👨‍👩‍👧‍👦 اختبار تنظيم الأسرة مع MySQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #e91e63; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .success-section { border-color: #28a745; }
        .warning-section { border-color: #ffc107; }
        .error-section { border-color: #dc3545; }
        .contraceptive-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .form-field {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .form-field label {
            width: 120px;
            font-weight: bold;
        }
        .form-field input {
            flex: 1;
            padding: 8px;
            border: 2px solid #e91e63;
            border-radius: 4px;
        }
        .contraceptive-item {
            background: white;
            border: 3px solid #e91e63;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(135deg, #fff 0%, #fce4ec 100%);
            box-shadow: 0 2px 8px rgba(233, 30, 99, 0.1);
        }
        .contraceptive-item h5 { margin: 0 0 10px 0; color: #e91e63; text-align: center; }
        .contraceptive-quantity-input { 
            width: 120px; padding: 12px; margin: 0 10px;
            border: 3px solid #e91e63; border-radius: 8px; text-align: center;
            font-size: 1.2em; font-weight: bold; background: #fff;
        }
        .contraceptive-quantity-input:focus { border-color: #c2185b; outline: none; }
        .month-name-input { 
            width: 200px; padding: 10px; margin: 10px 0;
            border: 2px solid #e91e63; border-radius: 5px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #e91e63;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #c2185b; }
        button.save { background: #28a745; }
        button.save:hover { background: #218838; }
        button.load { background: #17a2b8; }
        button.load:hover { background: #138496; }
        button.clear { background: #dc3545; }
        button.clear:hover { background: #c82333; }
        button.add { background: #ffc107; color: #212529; }
        button.add:hover { background: #e0a800; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { 
            background: #f8f9fa; padding: 15px; border-radius: 5px; 
            overflow-x: auto; font-size: 12px; white-space: pre-wrap;
        }
        .grid-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .month-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: #f8f9fa; }
        .test-steps {
            background: #fce4ec;
            border-left: 4px solid #e91e63;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>👨‍👩‍👧‍👦 اختبار نظام تنظيم الأسرة مع MySQL فقط</h1>
    
    <div class="test-section success-section">
        <h2>✅ المزايا الجديدة لتنظيم الأسرة</h2>
        <ul>
            <li>🌍 <strong>عمل عالمي:</strong> قائمة تقنيات منع الحمل محفوظة في MySQL</li>
            <li>🔄 <strong>مزامنة تلقائية:</strong> التخطيط الشهري متزامن بين الأجهزة</li>
            <li>💾 <strong>حفظ دائم:</strong> لا فقدان للبيانات حتى لو تم مسح المتصفح</li>
            <li>👥 <strong>متعدد المستخدمين:</strong> كل مستخدم له تقنياته الخاصة</li>
            <li>📊 <strong>حساب تلقائي:</strong> المخزون الإجمالي يُحسب من الأشهر الثلاثة</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 خطوات الاختبار</h2>
        <div class="test-steps">
            <h3>الخطوة 1: إضافة تقنيات منع حمل جديدة</h3>
            <p>استخدم النموذج أدناه لإضافة تقنيات منع حمل جديدة</p>
        </div>
        <div class="test-steps">
            <h3>الخطوة 2: تحديد كميات شهرية</h3>
            <p>أدخل كميات التقنيات لكل شهر</p>
        </div>
        <div class="test-steps">
            <h3>الخطوة 3: حفظ في MySQL</h3>
            <p>اضغط "💾 حفظ التخطيط الشهري" لحفظ البيانات</p>
        </div>
        <div class="test-steps">
            <h3>الخطوة 4: اختبار متصفح جديد</h3>
            <p>افتح متصفح جديد وتحقق من ظهور نفس البيانات</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>➕ إضافة تقنية منع حمل جديدة</h2>
        <div class="contraceptive-form">
            <div class="form-field">
                <label for="contraceptiveName">اسم التقنية:</label>
                <input type="text" id="contraceptiveName" placeholder="مثل: حبوب منع الحمل، اللولب النحاسي">
            </div>
            <div class="form-field">
                <label>&nbsp;</label>
                <button onclick="addContraceptive()" class="add">➕ إضافة التقنية</button>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 أدوات التحكم</h2>
        <button onclick="loadContraceptiveList()">📂 تحميل قائمة التقنيات</button>
        <button onclick="loadFamilyPlanningPlanning()">📋 تحميل التخطيط الشهري</button>
        <button onclick="saveFamilyPlanningPlanning()" class="save">💾 حفظ التخطيط الشهري</button>
        <button onclick="simulateNewBrowser()">🌐 محاكاة متصفح جديد</button>
        <button onclick="clearAllData()" class="clear">🗑️ مسح جميع البيانات</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار نظام تنظيم الأسرة مع MySQL
    </div>
    
    <div class="grid-container">
        <div>
            <div class="test-section">
                <h3>📋 قائمة تقنيات منع الحمل</h3>
                <div id="contraceptiveList">
                    <p>لا توجد تقنيات بعد...</p>
                </div>
            </div>
            
            <div class="test-section">
                <h3>📅 التخطيط الشهري</h3>
                
                <div class="month-section">
                    <h4>الشهر الأول</h4>
                    <input type="text" class="month-name-input" id="familyPlanningMonth1Name" 
                           placeholder="اسم الشهر الأول">
                    <div id="familyPlanningMonth1Grid"></div>
                </div>
                
                <div class="month-section">
                    <h4>الشهر الثاني</h4>
                    <input type="text" class="month-name-input" id="familyPlanningMonth2Name" 
                           placeholder="اسم الشهر الثاني">
                    <div id="familyPlanningMonth2Grid"></div>
                </div>
            </div>
        </div>
        
        <div>
            <div class="test-section">
                <h3>📊 حالة البيانات</h3>
                <div id="dataStatus"></div>
                
                <h4>📝 قائمة التقنيات الحالية:</h4>
                <pre id="currentContraceptives">لا توجد تقنيات</pre>
                
                <h4>📅 التخطيط الشهري الحالي:</h4>
                <pre id="currentPlanning">لا يوجد تخطيط</pre>
                
                <h4>📦 المخزون الإجمالي:</h4>
                <pre id="totalStock">لا يوجد مخزون</pre>
            </div>
        </div>
    </div>

    <script>
        let contraceptiveList = [];
        let familyPlanningMonthlyPlanning = {
            month1: { name: '', contraceptives: {} },
            month2: { name: '', contraceptives: {} }
        };
        let familyPlanningStock = {};
        let currentUser = { id: 'family_planning_test_user', center_id: '1' };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function showToast(message, type = 'info') {
            updateStatus(message, type === 'success' ? 'pass' : type === 'error' ? 'fail' : 'info');
        }
        
        async function addContraceptive() {
            const nameInput = document.getElementById('contraceptiveName');
            const name = nameInput.value.trim();
            
            if (!name) {
                showToast('يرجى إدخال اسم التقنية', 'error');
                return;
            }
            
            if (contraceptiveList.some(contraceptive => contraceptive.name.toLowerCase() === name.toLowerCase())) {
                showToast('هذه التقنية موجودة بالفعل في القائمة', 'error');
                return;
            }
            
            try {
                updateStatus('جاري إضافة التقنية في MySQL...', 'info');
                
                const response = await fetch('family-planning-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'add_contraceptive',
                        user_id: currentUser.id,
                        center_id: currentUser.center_id,
                        name: name
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة إضافة التقنية:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    contraceptiveList.push(data.contraceptive);
                    
                    // إضافة للتخطيط الشهري
                    familyPlanningMonthlyPlanning.month1.contraceptives[data.contraceptive.id] = 0;
                    familyPlanningMonthlyPlanning.month2.contraceptives[data.contraceptive.id] = 0;
                    
                    displayContraceptiveList();
                    displayContraceptiveGrids();
                    updateDataDisplay();
                    
                    nameInput.value = '';
                    
                    showToast(`تم إضافة التقنية "${name}" بنجاح`, 'success');
                } else {
                    showToast('فشل في إضافة التقنية: ' + data.message, 'error');
                }
            } catch (error) {
                showToast('خطأ في إضافة التقنية: ' + error.message, 'error');
            }
        }
        
        async function loadContraceptiveList() {
            try {
                updateStatus('جاري تحميل قائمة التقنيات من MySQL...', 'info');
                
                const response = await fetch('family-planning-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'load_contraceptive_list',
                        user_id: currentUser.id
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة تحميل التقنيات:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    contraceptiveList = data.contraceptives || [];
                    displayContraceptiveList();
                    updateDataDisplay();
                    showToast(`تم تحميل ${contraceptiveList.length} تقنية من MySQL`, 'success');
                } else {
                    showToast('لا توجد تقنيات محفوظة', 'warning');
                }
            } catch (error) {
                showToast('خطأ في تحميل التقنيات: ' + error.message, 'error');
            }
        }
        
        async function loadFamilyPlanningPlanning() {
            try {
                updateStatus('جاري تحميل التخطيط الشهري من MySQL...', 'info');
                
                const response = await fetch('family-planning-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'load_family_planning_monthly_planning',
                        user_id: currentUser.id
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة تحميل التخطيط:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success && data.planning && Object.keys(data.planning).length > 0) {
                    familyPlanningMonthlyPlanning = data.planning;
                    
                    document.getElementById('familyPlanningMonth1Name').value = familyPlanningMonthlyPlanning.month1?.name || '';
                    document.getElementById('familyPlanningMonth2Name').value = familyPlanningMonthlyPlanning.month2?.name || '';
                    
                    displayContraceptiveGrids();
                    calculateTotalStock();
                    updateDataDisplay();
                    
                    showToast('تم تحميل التخطيط الشهري من MySQL', 'success');
                } else {
                    showToast('لا يوجد تخطيط شهري محفوظ', 'warning');
                }
            } catch (error) {
                showToast('خطأ في تحميل التخطيط: ' + error.message, 'error');
            }
        }
        
        async function saveFamilyPlanningPlanning() {
            try {
                // تحديث أسماء الأشهر
                familyPlanningMonthlyPlanning.month1.name = document.getElementById('familyPlanningMonth1Name').value;
                familyPlanningMonthlyPlanning.month2.name = document.getElementById('familyPlanningMonth2Name').value;
                
                // تحديث الكميات من الحقول
                contraceptiveList.forEach(contraceptive => {
                    ['month1', 'month2'].forEach(month => {
                        const input = document.getElementById(`familyPlanning${month}_${contraceptive.id}`);
                        if (input) {
                            familyPlanningMonthlyPlanning[month].contraceptives[contraceptive.id] = parseInt(input.value) || 0;
                        }
                    });
                });
                
                updateStatus('جاري حفظ التخطيط الشهري في MySQL...', 'info');
                
                const response = await fetch('family-planning-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'save_family_planning_monthly_planning',
                        user_id: currentUser.id,
                        center_id: currentUser.center_id,
                        planning: familyPlanningMonthlyPlanning
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة حفظ التخطيط:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    calculateTotalStock();
                    updateDataDisplay();
                    showToast('تم حفظ التخطيط الشهري في MySQL بنجاح', 'success');
                } else {
                    showToast('فشل في حفظ التخطيط: ' + data.message, 'error');
                }
            } catch (error) {
                showToast('خطأ في حفظ التخطيط: ' + error.message, 'error');
            }
        }
        
        function displayContraceptiveList() {
            const container = document.getElementById('contraceptiveList');
            
            if (contraceptiveList.length === 0) {
                container.innerHTML = '<p>لا توجد تقنيات بعد...</p>';
                return;
            }
            
            let html = '';
            contraceptiveList.forEach(contraceptive => {
                html += `
                    <div class="contraceptive-item">
                        <h5>🔹 ${contraceptive.name}</h5>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function displayContraceptiveGrids() {
            ['month1', 'month2'].forEach(month => {
                const grid = document.getElementById(`familyPlanning${month.charAt(0).toUpperCase() + month.slice(1)}Grid`);
                grid.innerHTML = '';
                
                contraceptiveList.forEach(contraceptive => {
                    const currentValue = familyPlanningMonthlyPlanning[month].contraceptives[contraceptive.id] || 0;
                    
                    const contraceptiveItem = document.createElement('div');
                    contraceptiveItem.className = 'contraceptive-item';
                    contraceptiveItem.innerHTML = `
                        <h5>🔹 ${contraceptive.name}</h5>
                        <div style="display: flex; align-items: center; justify-content: center; gap: 15px; flex-wrap: wrap;">
                            <label style="font-weight: bold; color: #333; font-size: 1.1em;">العدد المطلوب:</label>
                            <input type="number" class="contraceptive-quantity-input" id="familyPlanning${month}_${contraceptive.id}"
                                   value="${currentValue}" min="0" max="10000"
                                   onchange="updateQuantity('${month}', '${contraceptive.id}')">
                            <span style="color: #666; font-size: 1em; font-weight: bold;">عدد</span>
                        </div>
                    `;
                    grid.appendChild(contraceptiveItem);
                });
            });
        }
        
        function updateQuantity(month, contraceptiveId) {
            const input = document.getElementById(`familyPlanning${month}_${contraceptiveId}`);
            const value = parseInt(input.value) || 0;
            
            familyPlanningMonthlyPlanning[month].contraceptives[contraceptiveId] = value;
            calculateTotalStock();
            updateDataDisplay();
        }
        
        function calculateTotalStock() {
            familyPlanningStock = {};
            
            contraceptiveList.forEach(contraceptive => {
                const month1Qty = familyPlanningMonthlyPlanning.month1.contraceptives[contraceptive.id] || 0;
                const month2Qty = familyPlanningMonthlyPlanning.month2.contraceptives[contraceptive.id] || 0;
                
                familyPlanningStock[contraceptive.id] = month1Qty + month2Qty;
            });
        }
        
        function updateDataDisplay() {
            document.getElementById('currentContraceptives').textContent = JSON.stringify(contraceptiveList, null, 2);
            document.getElementById('currentPlanning').textContent = JSON.stringify(familyPlanningMonthlyPlanning, null, 2);
            document.getElementById('totalStock').textContent = JSON.stringify(familyPlanningStock, null, 2);
            
            const statusDiv = document.getElementById('dataStatus');
            const totalContraceptives = contraceptiveList.length;
            const totalStock = Object.values(familyPlanningStock).reduce((sum, qty) => sum + qty, 0);
            
            statusDiv.innerHTML = `
                <div style="background: #e9ecef; padding: 10px; border-radius: 5px;">
                    <strong>📊 الإحصائيات:</strong><br>
                    👨‍👩‍👧‍👦 عدد التقنيات: ${totalContraceptives}<br>
                    📦 إجمالي المخزون: ${totalStock}<br>
                    🗄️ مصدر البيانات: MySQL فقط<br>
                    👤 معرف المستخدم: ${currentUser.id}
                </div>
            `;
        }
        
        function simulateNewBrowser() {
            updateStatus('محاكاة متصفح جديد - مسح البيانات المحلية...', 'warning');
            
            contraceptiveList = [];
            familyPlanningMonthlyPlanning = {
                month1: { name: '', contraceptives: {} },
                month2: { name: '', contraceptives: {} }
            };
            familyPlanningStock = {};
            
            document.getElementById('familyPlanningMonth1Name').value = '';
            document.getElementById('familyPlanningMonth2Name').value = '';
            
            displayContraceptiveList();
            displayContraceptiveGrids();
            updateDataDisplay();
            
            setTimeout(() => {
                loadContraceptiveList();
                setTimeout(() => {
                    loadFamilyPlanningPlanning();
                }, 1000);
            }, 1000);
        }
        
        async function clearAllData() {
            if (!confirm('هل أنت متأكد من مسح جميع بيانات تنظيم الأسرة؟')) return;
            
            try {
                // مسح التخطيط الشهري
                await saveFamilyPlanningPlanning();
                
                // مسح التقنيات واحدة تلو الأخرى
                for (const contraceptive of contraceptiveList) {
                    await fetch('family-planning-api.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            action: 'delete_contraceptive',
                            user_id: currentUser.id,
                            contraceptive_id: contraceptive.id
                        })
                    });
                }
                
                simulateNewBrowser();
                showToast('تم مسح جميع البيانات', 'success');
                
            } catch (error) {
                showToast('خطأ في مسح البيانات: ' + error.message, 'error');
            }
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            updateDataDisplay();
            updateStatus('جاهز لاختبار نظام تنظيم الأسرة مع MySQL فقط', 'info');
        };
    </script>
</body>
</html>
