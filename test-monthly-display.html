<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار عرض التخطيط الشهري</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .month-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
        .vaccine-item { margin: 10px 0; padding: 10px; background: #f8f9fa; }
        .stock-input { width: 80px; padding: 5px; margin: 0 10px; }
        button { padding: 10px 20px; margin: 10px; }
    </style>
</head>
<body>
    <h1>اختبار عرض التخطيط الشهري للقاحات</h1>
    
    <button onclick="initializeData()">تهيئة البيانات</button>
    <button onclick="displayData()">عرض البيانات</button>
    <button onclick="saveTestData()">حفظ بيانات تجريبية</button>
    <button onclick="loadTestData()">تحميل البيانات</button>
    
    <div id="month1Section" class="month-section">
        <h3>الشهر الأول</h3>
        <input type="text" id="month1Name" placeholder="اسم الشهر" style="width: 200px; margin-bottom: 10px;">
        <div id="month1Grid"></div>
    </div>
    
    <div id="month2Section" class="month-section">
        <h3>الشهر الثاني</h3>
        <input type="text" id="month2Name" placeholder="اسم الشهر" style="width: 200px; margin-bottom: 10px;">
        <div id="month2Grid"></div>
    </div>
    
    <div id="month3Section" class="month-section">
        <h3>الشهر الثالث</h3>
        <input type="text" id="month3Name" placeholder="اسم الشهر" style="width: 200px; margin-bottom: 10px;">
        <div id="month3Grid"></div>
    </div>
    
    <div id="output" style="margin-top: 20px; padding: 10px; background: #e9ecef;"></div>

    <script>
        let monthlyPlanning = {};
        
        const vaccineDefinitions = {
            'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B' },
            'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)' },
            'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)' },
            'VPI': { name: 'شلل الأطفال المحقون', frenchName: 'VPI (Polio Injectable)' },
            'Rota': { name: 'الروتا', frenchName: 'Rotavirus' },
            'Penta': { name: 'الخماسي', frenchName: 'Pentavalent' },
            'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)' },
            'Pneumo': { name: 'المكورات الرئوية', frenchName: 'Pneumocoque' },
            'DTC': { name: 'الثلاثي', frenchName: 'DTC (Diphtérie-Tétanos-Coqueluche)' },
            'VAT': { name: 'الكزاز للحوامل', frenchName: 'VAT (Tétanos femmes enceintes)' }
        };
        
        function initializeData() {
            const defaultVaccines = {};
            Object.keys(vaccineDefinitions).forEach(key => {
                defaultVaccines[key] = 0;
            });
            
            monthlyPlanning = {
                month1: { name: '', vaccines: {...defaultVaccines} },
                month2: { name: '', vaccines: {...defaultVaccines} },
                month3: { name: '', vaccines: {...defaultVaccines} }
            };
            
            console.log('تم تهيئة البيانات:', monthlyPlanning);
            displayData();
        }
        
        function displayData() {
            ['month1', 'month2', 'month3'].forEach(month => {
                const grid = document.getElementById(`${month}Grid`);
                grid.innerHTML = '';
                
                if (!monthlyPlanning[month]) {
                    monthlyPlanning[month] = { name: '', vaccines: {} };
                }
                
                Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                    const vaccine = vaccineDefinitions[vaccineKey];
                    const currentValue = monthlyPlanning[month].vaccines[vaccineKey] || 0;
                    
                    const vaccineItem = document.createElement('div');
                    vaccineItem.className = 'vaccine-item';
                    vaccineItem.innerHTML = `
                        <strong>${vaccine.name}</strong> (${vaccine.frenchName})
                        <input type="number" class="stock-input" id="${month}_${vaccineKey}"
                               value="${currentValue}" min="0" max="1000"
                               onchange="updateVaccine('${month}', '${vaccineKey}')">
                        قارورة
                    `;
                    grid.appendChild(vaccineItem);
                });
            });
        }
        
        function updateVaccine(month, vaccineKey) {
            const input = document.getElementById(`${month}_${vaccineKey}`);
            const value = parseInt(input.value) || 0;
            
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].vaccines[vaccineKey] = value;
            
            console.log(`تحديث ${month}_${vaccineKey} = ${value}`);
            document.getElementById('output').innerHTML = `
                <strong>آخر تحديث:</strong> ${month}_${vaccineKey} = ${value}<br>
                <strong>البيانات الحالية:</strong><br>
                <pre>${JSON.stringify(monthlyPlanning, null, 2)}</pre>
            `;
        }
        
        function saveTestData() {
            // حفظ بيانات تجريبية
            monthlyPlanning.month1.name = 'يناير 2025';
            monthlyPlanning.month1.vaccines.HB1 = 50;
            monthlyPlanning.month1.vaccines.BCG = 30;
            
            monthlyPlanning.month2.name = 'فبراير 2025';
            monthlyPlanning.month2.vaccines.HB1 = 60;
            monthlyPlanning.month2.vaccines.VPO = 40;
            
            monthlyPlanning.month3.name = 'مارس 2025';
            monthlyPlanning.month3.vaccines.Penta = 35;
            monthlyPlanning.month3.vaccines.RR = 25;
            
            localStorage.setItem('test_monthlyPlanning', JSON.stringify(monthlyPlanning));
            
            document.getElementById('month1Name').value = monthlyPlanning.month1.name;
            document.getElementById('month2Name').value = monthlyPlanning.month2.name;
            document.getElementById('month3Name').value = monthlyPlanning.month3.name;
            
            displayData();
            console.log('تم حفظ البيانات التجريبية');
        }
        
        function loadTestData() {
            const saved = localStorage.getItem('test_monthlyPlanning');
            if (saved) {
                monthlyPlanning = JSON.parse(saved);
                
                document.getElementById('month1Name').value = monthlyPlanning.month1.name || '';
                document.getElementById('month2Name').value = monthlyPlanning.month2.name || '';
                document.getElementById('month3Name').value = monthlyPlanning.month3.name || '';
                
                displayData();
                console.log('تم تحميل البيانات:', monthlyPlanning);
            } else {
                console.log('لا توجد بيانات محفوظة');
            }
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            initializeData();
        };
    </script>
</body>
</html>
