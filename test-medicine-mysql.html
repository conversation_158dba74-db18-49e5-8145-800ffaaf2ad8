<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💊 اختبار الأدوية مع MySQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .success-section { border-color: #28a745; }
        .warning-section { border-color: #ffc107; }
        .error-section { border-color: #dc3545; }
        .medicine-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .form-field {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .form-field label {
            width: 120px;
            font-weight: bold;
        }
        .form-field input {
            flex: 1;
            padding: 8px;
            border: 2px solid #ced4da;
            border-radius: 4px;
        }
        .medicine-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .medicine-item h5 { margin: 0 0 10px 0; color: #17a2b8; }
        .stock-input { 
            width: 80px; padding: 8px; margin: 0 10px;
            border: 2px solid #ced4da; border-radius: 4px; text-align: center;
            font-size: 16px; font-weight: bold;
        }
        .stock-input:focus { border-color: #007bff; outline: none; }
        .month-name-input { 
            width: 200px; padding: 10px; margin: 10px 0;
            border: 2px solid #ced4da; border-radius: 5px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.save { background: #28a745; }
        button.save:hover { background: #218838; }
        button.load { background: #17a2b8; }
        button.load:hover { background: #138496; }
        button.clear { background: #dc3545; }
        button.clear:hover { background: #c82333; }
        button.add { background: #ffc107; color: #212529; }
        button.add:hover { background: #e0a800; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { 
            background: #f8f9fa; padding: 15px; border-radius: 5px; 
            overflow-x: auto; font-size: 12px; white-space: pre-wrap;
        }
        .grid-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .month-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: #f8f9fa; }
        .test-steps {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>💊 اختبار نظام الأدوية مع MySQL فقط</h1>
    
    <div class="test-section success-section">
        <h2>✅ المزايا الجديدة للأدوية</h2>
        <ul>
            <li>🌍 <strong>عمل عالمي:</strong> قائمة الأدوية محفوظة في MySQL</li>
            <li>🔄 <strong>مزامنة تلقائية:</strong> التخطيط الشهري متزامن بين الأجهزة</li>
            <li>💾 <strong>حفظ دائم:</strong> لا فقدان للبيانات حتى لو تم مسح المتصفح</li>
            <li>👥 <strong>متعدد المستخدمين:</strong> كل مستخدم له أدويته الخاصة</li>
            <li>📊 <strong>حساب تلقائي:</strong> المخزون الإجمالي يُحسب من الأشهر الثلاثة</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 خطوات الاختبار</h2>
        <div class="test-steps">
            <h3>الخطوة 1: إضافة أدوية جديدة</h3>
            <p>استخدم النموذج أدناه لإضافة أدوية جديدة</p>
        </div>
        <div class="test-steps">
            <h3>الخطوة 2: تحديد كميات شهرية</h3>
            <p>أدخل كميات الأدوية لكل شهر</p>
        </div>
        <div class="test-steps">
            <h3>الخطوة 3: حفظ في MySQL</h3>
            <p>اضغط "💾 حفظ التخطيط الشهري" لحفظ البيانات</p>
        </div>
        <div class="test-steps">
            <h3>الخطوة 4: اختبار متصفح جديد</h3>
            <p>افتح متصفح جديد وتحقق من ظهور نفس البيانات</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>➕ إضافة دواء جديد</h2>
        <div class="medicine-form">
            <div class="form-field">
                <label for="medicineName">اسم الدواء:</label>
                <input type="text" id="medicineName" placeholder="أدخل اسم الدواء">
            </div>
            <div class="form-field">
                <label for="medicineUnit">الوحدة:</label>
                <input type="text" id="medicineUnit" placeholder="مثل: قرص، شراب، أمبولة">
            </div>
            <div class="form-field">
                <label>&nbsp;</label>
                <button onclick="addMedicine()" class="add">➕ إضافة الدواء</button>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 أدوات التحكم</h2>
        <button onclick="loadMedicineList()">📂 تحميل قائمة الأدوية</button>
        <button onclick="loadMedicinePlanning()">📋 تحميل التخطيط الشهري</button>
        <button onclick="saveMedicinePlanning()" class="save">💾 حفظ التخطيط الشهري</button>
        <button onclick="simulateNewBrowser()">🌐 محاكاة متصفح جديد</button>
        <button onclick="clearAllData()" class="clear">🗑️ مسح جميع البيانات</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار نظام الأدوية مع MySQL
    </div>
    
    <div class="grid-container">
        <div>
            <div class="test-section">
                <h3>📋 قائمة الأدوية</h3>
                <div id="medicineList">
                    <p>لا توجد أدوية بعد...</p>
                </div>
            </div>
            
            <div class="test-section">
                <h3>📅 التخطيط الشهري</h3>
                
                <div class="month-section">
                    <h4>الشهر الأول</h4>
                    <input type="text" class="month-name-input" id="medicineMonth1Name" 
                           placeholder="اسم الشهر الأول">
                    <div id="medicineMonth1Grid"></div>
                </div>
                
                <div class="month-section">
                    <h4>الشهر الثاني</h4>
                    <input type="text" class="month-name-input" id="medicineMonth2Name" 
                           placeholder="اسم الشهر الثاني">
                    <div id="medicineMonth2Grid"></div>
                </div>
            </div>
        </div>
        
        <div>
            <div class="test-section">
                <h3>📊 حالة البيانات</h3>
                <div id="dataStatus"></div>
                
                <h4>📝 قائمة الأدوية الحالية:</h4>
                <pre id="currentMedicines">لا توجد أدوية</pre>
                
                <h4>📅 التخطيط الشهري الحالي:</h4>
                <pre id="currentPlanning">لا يوجد تخطيط</pre>
                
                <h4>📦 المخزون الإجمالي:</h4>
                <pre id="totalStock">لا يوجد مخزون</pre>
            </div>
        </div>
    </div>

    <script>
        let medicineList = [];
        let medicineMonthlyPlanning = {
            month1: { name: '', medicines: {} },
            month2: { name: '', medicines: {} }
        };
        let medicineStock = {};
        let currentUser = { id: 'medicine_test_user', center_id: '1' };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function showToast(message, type = 'info') {
            updateStatus(message, type === 'success' ? 'pass' : type === 'error' ? 'fail' : 'info');
        }
        
        async function addMedicine() {
            const nameInput = document.getElementById('medicineName');
            const unitInput = document.getElementById('medicineUnit');
            
            const name = nameInput.value.trim();
            const unit = unitInput.value.trim();
            
            if (!name || !unit) {
                showToast('يرجى إدخال اسم الدواء والوحدة', 'error');
                return;
            }
            
            if (medicineList.some(med => med.name.toLowerCase() === name.toLowerCase())) {
                showToast('هذا الدواء موجود بالفعل في القائمة', 'error');
                return;
            }
            
            try {
                updateStatus('جاري إضافة الدواء في MySQL...', 'info');
                
                const response = await fetch('medicines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'add_medicine',
                        user_id: currentUser.id,
                        center_id: currentUser.center_id,
                        name: name,
                        unit: unit
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة إضافة الدواء:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    medicineList.push(data.medicine);
                    
                    // إضافة للتخطيط الشهري
                    medicineMonthlyPlanning.month1.medicines[data.medicine.id] = 0;
                    medicineMonthlyPlanning.month2.medicines[data.medicine.id] = 0;
                    
                    displayMedicineList();
                    displayMedicineGrids();
                    updateDataDisplay();
                    
                    nameInput.value = '';
                    unitInput.value = '';
                    
                    showToast(`تم إضافة الدواء "${name}" بنجاح`, 'success');
                } else {
                    showToast('فشل في إضافة الدواء: ' + data.message, 'error');
                }
            } catch (error) {
                showToast('خطأ في إضافة الدواء: ' + error.message, 'error');
            }
        }
        
        async function loadMedicineList() {
            try {
                updateStatus('جاري تحميل قائمة الأدوية من MySQL...', 'info');
                
                const response = await fetch('medicines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'load_medicine_list',
                        user_id: currentUser.id
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة تحميل الأدوية:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    medicineList = data.medicines || [];
                    displayMedicineList();
                    updateDataDisplay();
                    showToast(`تم تحميل ${medicineList.length} دواء من MySQL`, 'success');
                } else {
                    showToast('لا توجد أدوية محفوظة', 'warning');
                }
            } catch (error) {
                showToast('خطأ في تحميل الأدوية: ' + error.message, 'error');
            }
        }
        
        async function loadMedicinePlanning() {
            try {
                updateStatus('جاري تحميل التخطيط الشهري من MySQL...', 'info');
                
                const response = await fetch('medicines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'load_medicine_monthly_planning',
                        user_id: currentUser.id
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة تحميل التخطيط:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success && data.planning && Object.keys(data.planning).length > 0) {
                    medicineMonthlyPlanning = data.planning;
                    
                    document.getElementById('medicineMonth1Name').value = medicineMonthlyPlanning.month1?.name || '';
                    document.getElementById('medicineMonth2Name').value = medicineMonthlyPlanning.month2?.name || '';
                    
                    displayMedicineGrids();
                    calculateTotalStock();
                    updateDataDisplay();
                    
                    showToast('تم تحميل التخطيط الشهري من MySQL', 'success');
                } else {
                    showToast('لا يوجد تخطيط شهري محفوظ', 'warning');
                }
            } catch (error) {
                showToast('خطأ في تحميل التخطيط: ' + error.message, 'error');
            }
        }
        
        async function saveMedicinePlanning() {
            try {
                // تحديث أسماء الأشهر
                medicineMonthlyPlanning.month1.name = document.getElementById('medicineMonth1Name').value;
                medicineMonthlyPlanning.month2.name = document.getElementById('medicineMonth2Name').value;
                
                // تحديث الكميات من الحقول
                medicineList.forEach(medicine => {
                    ['month1', 'month2'].forEach(month => {
                        const input = document.getElementById(`medicine${month}_${medicine.id}`);
                        if (input) {
                            medicineMonthlyPlanning[month].medicines[medicine.id] = parseInt(input.value) || 0;
                        }
                    });
                });
                
                updateStatus('جاري حفظ التخطيط الشهري في MySQL...', 'info');
                
                const response = await fetch('medicines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'save_medicine_monthly_planning',
                        user_id: currentUser.id,
                        center_id: currentUser.center_id,
                        planning: medicineMonthlyPlanning
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة حفظ التخطيط:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    calculateTotalStock();
                    updateDataDisplay();
                    showToast('تم حفظ التخطيط الشهري في MySQL بنجاح', 'success');
                } else {
                    showToast('فشل في حفظ التخطيط: ' + data.message, 'error');
                }
            } catch (error) {
                showToast('خطأ في حفظ التخطيط: ' + error.message, 'error');
            }
        }
        
        function displayMedicineList() {
            const container = document.getElementById('medicineList');
            
            if (medicineList.length === 0) {
                container.innerHTML = '<p>لا توجد أدوية بعد...</p>';
                return;
            }
            
            let html = '';
            medicineList.forEach(medicine => {
                html += `
                    <div class="medicine-item">
                        <h5>${medicine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d;">
                            الوحدة: ${medicine.unit}
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function displayMedicineGrids() {
            ['month1', 'month2'].forEach(month => {
                const grid = document.getElementById(`medicine${month.charAt(0).toUpperCase() + month.slice(1)}Grid`);
                grid.innerHTML = '';
                
                medicineList.forEach(medicine => {
                    const currentValue = medicineMonthlyPlanning[month].medicines[medicine.id] || 0;
                    
                    const medicineItem = document.createElement('div');
                    medicineItem.className = 'medicine-item';
                    medicineItem.innerHTML = `
                        <h5>${medicine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; margin-bottom: 8px;">
                            الوحدة: ${medicine.unit}
                        </div>
                        <div>
                            <input type="number" class="stock-input" id="medicine${month}_${medicine.id}"
                                   value="${currentValue}" min="0" max="10000"
                                   onchange="updateQuantity('${month}', '${medicine.id}')">
                            <span>${medicine.unit}</span>
                        </div>
                    `;
                    grid.appendChild(medicineItem);
                });
            });
        }
        
        function updateQuantity(month, medicineId) {
            const input = document.getElementById(`medicine${month}_${medicineId}`);
            const value = parseInt(input.value) || 0;
            
            medicineMonthlyPlanning[month].medicines[medicineId] = value;
            calculateTotalStock();
            updateDataDisplay();
        }
        
        function calculateTotalStock() {
            medicineStock = {};
            
            medicineList.forEach(medicine => {
                const month1Qty = medicineMonthlyPlanning.month1.medicines[medicine.id] || 0;
                const month2Qty = medicineMonthlyPlanning.month2.medicines[medicine.id] || 0;
                
                medicineStock[medicine.id] = month1Qty + month2Qty;
            });
        }
        
        function updateDataDisplay() {
            document.getElementById('currentMedicines').textContent = JSON.stringify(medicineList, null, 2);
            document.getElementById('currentPlanning').textContent = JSON.stringify(medicineMonthlyPlanning, null, 2);
            document.getElementById('totalStock').textContent = JSON.stringify(medicineStock, null, 2);
            
            const statusDiv = document.getElementById('dataStatus');
            const totalMedicines = medicineList.length;
            const totalStock = Object.values(medicineStock).reduce((sum, qty) => sum + qty, 0);
            
            statusDiv.innerHTML = `
                <div style="background: #e9ecef; padding: 10px; border-radius: 5px;">
                    <strong>📊 الإحصائيات:</strong><br>
                    💊 عدد الأدوية: ${totalMedicines}<br>
                    📦 إجمالي المخزون: ${totalStock}<br>
                    🗄️ مصدر البيانات: MySQL فقط<br>
                    👤 معرف المستخدم: ${currentUser.id}
                </div>
            `;
        }
        
        function simulateNewBrowser() {
            updateStatus('محاكاة متصفح جديد - مسح البيانات المحلية...', 'warning');
            
            medicineList = [];
            medicineMonthlyPlanning = {
                month1: { name: '', medicines: {} },
                month2: { name: '', medicines: {} }
            };
            medicineStock = {};
            
            document.getElementById('medicineMonth1Name').value = '';
            document.getElementById('medicineMonth2Name').value = '';
            
            displayMedicineList();
            displayMedicineGrids();
            updateDataDisplay();
            
            setTimeout(() => {
                loadMedicineList();
                setTimeout(() => {
                    loadMedicinePlanning();
                }, 1000);
            }, 1000);
        }
        
        async function clearAllData() {
            if (!confirm('هل أنت متأكد من مسح جميع بيانات الأدوية؟')) return;
            
            try {
                // مسح التخطيط الشهري
                await saveMedicinePlanning();
                
                // مسح الأدوية واحد تلو الآخر
                for (const medicine of medicineList) {
                    await fetch('vaccines-api.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            action: 'delete_medicine',
                            user_id: currentUser.id,
                            medicine_id: medicine.id
                        })
                    });
                }
                
                simulateNewBrowser();
                showToast('تم مسح جميع البيانات', 'success');
                
            } catch (error) {
                showToast('خطأ في مسح البيانات: ' + error.message, 'error');
            }
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            updateDataDisplay();
            updateStatus('جاهز لاختبار نظام الأدوية مع MySQL فقط', 'info');
        };
    </script>
</body>
</html>
