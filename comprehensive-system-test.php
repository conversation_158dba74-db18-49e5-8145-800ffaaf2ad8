<?php
/**
 * Comprehensive Healthcare Management System Test
 * اختبار شامل لنظام إدارة المراكز الصحية
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'system_info' => [
        'php_version' => PHP_VERSION,
        'server' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
        'test_version' => '1.0'
    ],
    'database_tests' => [],
    'api_tests' => [],
    'functionality_tests' => [],
    'security_tests' => [],
    'performance_tests' => [],
    'overall_status' => 'UNKNOWN',
    'recommendations' => []
];

try {
    // ===========================================
    // 1. DATABASE CONNECTIVITY TESTS
    // ===========================================
    
    $response['database_tests']['connection'] = [];
    
    // Test 1.1: Database Connection
    try {
        require_once 'config/database-live.php';
        $pdo = getDatabaseConnection();
        
        $response['database_tests']['connection']['status'] = 'SUCCESS';
        $response['database_tests']['connection']['message'] = 'Database connected successfully';
        
        // Get database info
        $stmt = $pdo->query("SELECT DATABASE() as db_name, VERSION() as mysql_version");
        $db_info = $stmt->fetch();
        $response['database_tests']['connection']['database_name'] = $db_info['db_name'];
        $response['database_tests']['connection']['mysql_version'] = $db_info['mysql_version'];
        
    } catch (Exception $e) {
        $response['database_tests']['connection']['status'] = 'FAILED';
        $response['database_tests']['connection']['error'] = $e->getMessage();
        throw new Exception('Database connection failed: ' . $e->getMessage());
    }
    
    // Test 1.2: Table Structure Verification
    $required_tables = [
        'users' => 'User management',
        'centers' => 'Healthcare centers',
        'children' => 'Children registry',
        'medicines' => 'Medicine inventory',
        'vaccines' => 'Vaccine management',
        'contraceptives' => 'Family planning',
        'tasks' => 'Task management',
        'messages' => 'Messaging system',
        'notifications' => 'Notification system',
        'monthly_planning' => 'Monthly planning',
        'medicine_list' => 'Medicine catalog',
        'vaccine_list' => 'Vaccine catalog',
        'contraceptive_list' => 'Contraceptive catalog'
    ];
    
    $stmt = $pdo->query("SHOW TABLES");
    $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $response['database_tests']['tables'] = [
        'total_existing' => count($existing_tables),
        'required_tables' => [],
        'missing_tables' => [],
        'extra_tables' => []
    ];
    
    foreach ($required_tables as $table => $description) {
        if (in_array($table, $existing_tables)) {
            // Get table info
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            
            $response['database_tests']['tables']['required_tables'][$table] = [
                'status' => 'EXISTS',
                'record_count' => $count,
                'description' => $description
            ];
        } else {
            $response['database_tests']['tables']['missing_tables'][] = $table;
        }
    }
    
    // ===========================================
    // 2. API CONNECTIVITY TESTS
    // ===========================================
    
    $api_endpoints = [
        'children-api.php' => [
            'name' => 'Children Management',
            'test_action' => 'load',
            'save_action' => 'save',
            'critical' => true
        ],
        'medicines-api.php' => [
            'name' => 'Medicine Management',
            'test_action' => 'test',
            'save_action' => 'add_medicine',
            'critical' => true
        ],
        'vaccines-api.php' => [
            'name' => 'Vaccine Management',
            'test_action' => 'load_vaccines',
            'save_action' => 'save_vaccine',
            'critical' => true
        ],
        'family-planning-api.php' => [
            'name' => 'Family Planning',
            'test_action' => 'test',
            'save_action' => 'add_contraceptive',
            'critical' => true
        ],
        'user-api-simple.php' => [
            'name' => 'User Management',
            'test_action' => 'test',
            'save_action' => null,
            'critical' => true
        ],
        'tasks-api.php' => [
            'name' => 'Task Management',
            'test_action' => 'test',
            'save_action' => 'save',
            'critical' => false
        ],
        'messages-api.php' => [
            'name' => 'Messaging System',
            'test_action' => 'test',
            'save_action' => 'send',
            'critical' => false
        ],
        'monthly-planning-api.php' => [
            'name' => 'Monthly Planning',
            'test_action' => 'test',
            'save_action' => 'save_monthly_planning',
            'critical' => false
        ],
        'vaccination-status-api.php' => [
            'name' => 'Vaccination Status',
            'test_action' => 'test',
            'save_action' => null,
            'critical' => false
        ],
        'user-management-api.php' => [
            'name' => 'Advanced User Management',
            'test_action' => 'load_all_users',
            'save_action' => null,
            'critical' => false
        ]
    ];
    
    $critical_apis_working = 0;
    $total_critical_apis = 0;
    $all_apis_working = 0;
    
    foreach ($api_endpoints as $api_file => $config) {
        if ($config['critical']) {
            $total_critical_apis++;
        }
        
        $api_result = [
            'name' => $config['name'],
            'critical' => $config['critical'],
            'connectivity_test' => 'UNKNOWN',
            'response_time' => 0,
            'error' => null
        ];
        
        // Test API connectivity
        $start_time = microtime(true);
        try {
            $test_data = [
                'action' => $config['test_action'],
                'user_id' => 'test_user_001',
                'center_id' => 1
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/$api_file");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $result = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);
            
            $end_time = microtime(true);
            $api_result['response_time'] = round(($end_time - $start_time) * 1000, 2); // ms
            
            if ($curl_error) {
                $api_result['connectivity_test'] = 'CURL_ERROR';
                $api_result['error'] = $curl_error;
            } elseif ($http_code !== 200) {
                $api_result['connectivity_test'] = 'HTTP_ERROR';
                $api_result['error'] = "HTTP $http_code";
            } else {
                $api_response = json_decode($result, true);
                if ($api_response && isset($api_response['success'])) {
                    $api_result['connectivity_test'] = $api_response['success'] ? 'SUCCESS' : 'API_ERROR';
                    if (!$api_response['success']) {
                        $api_result['error'] = $api_response['message'] ?? 'Unknown API error';
                    } else {
                        $all_apis_working++;
                        if ($config['critical']) {
                            $critical_apis_working++;
                        }
                    }
                } else {
                    $api_result['connectivity_test'] = 'INVALID_RESPONSE';
                    $api_result['error'] = 'Invalid JSON response';
                }
            }
            
        } catch (Exception $e) {
            $api_result['connectivity_test'] = 'EXCEPTION';
            $api_result['error'] = $e->getMessage();
        }
        
        $response['api_tests'][$api_file] = $api_result;
    }
    
    // ===========================================
    // 3. FUNCTIONALITY TESTS
    // ===========================================
    
    $response['functionality_tests'] = [
        'user_authentication' => 'UNKNOWN',
        'data_persistence' => 'UNKNOWN',
        'crud_operations' => []
    ];
    
    // Test 3.1: User Authentication
    try {
        $auth_data = ['action' => 'load_all_users'];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/user-api-simple.php");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($auth_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        curl_close($ch);
        
        $users_response = json_decode($result, true);
        if ($users_response && $users_response['success'] && isset($users_response['users'])) {
            $user_count = count($users_response['users']);
            $response['functionality_tests']['user_authentication'] = $user_count > 0 ? 'SUCCESS' : 'NO_USERS';
            $response['functionality_tests']['available_users'] = $user_count;
        } else {
            $response['functionality_tests']['user_authentication'] = 'FAILED';
        }
    } catch (Exception $e) {
        $response['functionality_tests']['user_authentication'] = 'ERROR';
    }
    
    // ===========================================
    // 4. PERFORMANCE TESTS
    // ===========================================
    
    $response['performance_tests'] = [
        'average_api_response_time' => 0,
        'database_query_performance' => 'UNKNOWN',
        'slowest_api' => null,
        'fastest_api' => null
    ];
    
    $response_times = array_column($response['api_tests'], 'response_time');
    $response_times = array_filter($response_times, function($time) { return $time > 0; });
    
    if (!empty($response_times)) {
        $response['performance_tests']['average_api_response_time'] = round(array_sum($response_times) / count($response_times), 2);
        
        $slowest_time = max($response_times);
        $fastest_time = min($response_times);
        
        foreach ($response['api_tests'] as $api => $data) {
            if ($data['response_time'] == $slowest_time) {
                $response['performance_tests']['slowest_api'] = ['api' => $api, 'time' => $slowest_time];
            }
            if ($data['response_time'] == $fastest_time) {
                $response['performance_tests']['fastest_api'] = ['api' => $api, 'time' => $fastest_time];
            }
        }
    }
    
    // Database performance test
    $start_time = microtime(true);
    $pdo->query("SELECT COUNT(*) FROM users");
    $db_time = round((microtime(true) - $start_time) * 1000, 2);
    $response['performance_tests']['database_query_performance'] = $db_time < 100 ? 'EXCELLENT' : ($db_time < 500 ? 'GOOD' : 'SLOW');
    $response['performance_tests']['database_response_time'] = $db_time;
    
    // ===========================================
    // 5. OVERALL ASSESSMENT
    // ===========================================
    
    $critical_success_rate = $total_critical_apis > 0 ? ($critical_apis_working / $total_critical_apis) * 100 : 0;
    $overall_success_rate = count($api_endpoints) > 0 ? ($all_apis_working / count($api_endpoints)) * 100 : 0;
    
    $response['summary'] = [
        'critical_apis_working' => "$critical_apis_working/$total_critical_apis",
        'critical_success_rate' => round($critical_success_rate, 1) . '%',
        'total_apis_working' => "$all_apis_working/" . count($api_endpoints),
        'overall_success_rate' => round($overall_success_rate, 1) . '%',
        'database_status' => $response['database_tests']['connection']['status'],
        'missing_tables' => count($response['database_tests']['tables']['missing_tables']),
        'system_ready' => false
    ];
    
    // Determine overall status
    if ($critical_success_rate >= 100 && $overall_success_rate >= 80) {
        $response['overall_status'] = 'EXCELLENT';
        $response['summary']['system_ready'] = true;
        $response['message'] = 'System is fully operational and ready for production use';
    } elseif ($critical_success_rate >= 80 && $overall_success_rate >= 60) {
        $response['overall_status'] = 'GOOD';
        $response['summary']['system_ready'] = true;
        $response['message'] = 'System is operational with minor issues';
    } elseif ($critical_success_rate >= 60) {
        $response['overall_status'] = 'NEEDS_ATTENTION';
        $response['message'] = 'System has significant issues that need attention';
    } else {
        $response['overall_status'] = 'CRITICAL_ISSUES';
        $response['message'] = 'System has critical issues and is not ready for use';
    }
    
    // Generate recommendations
    if (count($response['database_tests']['tables']['missing_tables']) > 0) {
        $response['recommendations'][] = 'Create missing database tables: ' . implode(', ', $response['database_tests']['tables']['missing_tables']);
    }
    
    if ($critical_success_rate < 100) {
        $response['recommendations'][] = 'Fix critical API issues before using the system';
    }
    
    if ($response['performance_tests']['average_api_response_time'] > 1000) {
        $response['recommendations'][] = 'Optimize API performance - average response time is too high';
    }
    
    if ($response['functionality_tests']['user_authentication'] !== 'SUCCESS') {
        $response['recommendations'][] = 'Fix user authentication system';
    }
    
    if (empty($response['recommendations'])) {
        $response['recommendations'][] = 'System is working well - ready for production use!';
    }
    
} catch (Exception $e) {
    $response['overall_status'] = 'SYSTEM_ERROR';
    $response['message'] = 'System test failed: ' . $e->getMessage();
    $response['error'] = $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
