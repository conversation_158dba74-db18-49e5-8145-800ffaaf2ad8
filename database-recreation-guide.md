# Database Recreation Guide
# دليل إعادة إنشاء قاعدة البيانات

## 🚀 Quick Start - البداية السريعة

### Option 1: Using the Web Interface (Recommended)
1. Open: `database-tools.html`
2. Go to "إعادة إنشاء قاعدة البيانات الكاملة" section
3. Enter your MySQL root password
4. Click "إعادة إنشاء (حذف الموجود)" for complete recreation
5. Then click "تهيئة البيانات" to populate with sample data

### Option 2: Direct API Calls
```
# Step 1: Recreate database
http://your-domain/recreate-database.php?action=recreate&root_password=YOUR_ROOT_PASSWORD&force=true

# Step 2: Initialize API data
http://your-domain/initialize-api-data.php
```

## 📋 What Gets Created

### Database Structure:
- **Database Name**: `csdb`
- **User**: `csdbuser` with password `j5aKN6lz5bsujTcWaYAd`
- **Character Set**: `utf8mb4_unicode_ci`

### Tables Created:
1. **centers** - المراكز الصحية
2. **users** - المستخدمين (الممرضين)
3. **children** - الأطفال
4. **vaccines** - اللقاحات
5. **vaccine_stock** - مخزون اللقاحات
6. **child_vaccinations** - تطعيمات الأطفال
7. **medicines** - الأدوية
8. **medicine_stock** - مخزون الأدوية
9. **contraceptives** - وسائل منع الحمل
10. **contraceptive_stock** - مخزون وسائل منع الحمل
11. **monthly_planning** - التخطيط الشهري
12. **messages** - الرسائل
13. **notifications** - الإشعارات
14. **tasks** - المهام
15. **user_settings** - إعدادات المستخدم

### Sample Data Included:
- **8 Medicines** with stock levels
- **7 Contraceptive methods** with stock
- **7 Essential vaccines** for children
- Required system tables

## 🔧 Troubleshooting

### Common Issues:

1. **"Access denied for user 'root'"**
   - Make sure you're using the correct MySQL root password
   - Try common passwords: empty, "root", "password", "123456"

2. **"Database already exists"**
   - Use `force=true` parameter to delete existing database
   - Or manually drop the database first

3. **"Permission denied"**
   - Make sure MySQL server is running
   - Check if you have administrative privileges

4. **"Table already exists"**
   - This is normal - the script uses `CREATE TABLE IF NOT EXISTS`
   - Existing tables won't be affected unless using force mode

### Manual Database Creation:

If automatic creation fails, you can manually run these commands in MySQL:

```sql
-- Create database
CREATE DATABASE csdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user
CREATE USER 'csdbuser'@'localhost' IDENTIFIED BY 'j5aKN6lz5bsujTcWaYAd';

-- Grant permissions
GRANT ALL PRIVILEGES ON csdb.* TO 'csdbuser'@'localhost';
FLUSH PRIVILEGES;

-- Use database
USE csdb;

-- Then run the contents of database_design.sql
```

## 📊 Verification

After recreation, verify everything works:

1. **Check Status**: Visit `db-status.php`
2. **Test Connection**: Visit `test-db-connection.php`
3. **Verify Tables**: Should show 15+ tables created
4. **Test APIs**: Try loading the main website

## 🔄 API Endpoints Affected

These APIs will work after database recreation:
- `medicines-api.php` - Medicine management
- `family-planning-api.php` - Contraceptive management
- `vaccination-api-simple.php` - Vaccination tracking
- `children-api.php` - Children registry
- `messages-api.php` - Messaging system

## 📱 Next Steps

1. **Test the main website** - All API errors should be resolved
2. **Add your data** - Start adding real medicines, children, etc.
3. **Configure users** - Add nurse accounts through the system
4. **Backup regularly** - Use the backup features in the admin panel

## 🆘 Need Help?

If you encounter issues:
1. Check the error logs in the browser console
2. Visit `database-tools.html` for diagnostic tools
3. Use `db-status.php` to check current status
4. Ensure MySQL server is running and accessible

---

**Note**: This process will completely recreate your database. Make sure to backup any important data before proceeding!
