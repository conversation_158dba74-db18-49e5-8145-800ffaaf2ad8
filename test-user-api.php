<?php
/**
 * Test User API
 * اختبار User API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'tests' => []
];

try {
    // Test 1: Database connection
    require_once 'config/database-live.php';
    $pdo = getDatabaseConnection();
    $response['tests'][] = ['test' => 'Database Connection', 'status' => 'SUCCESS'];
    
    // Test 2: Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $table_exists = $stmt->rowCount() > 0;
    $response['tests'][] = ['test' => 'Users Table Exists', 'status' => $table_exists ? 'SUCCESS' : 'FAILED'];
    
    if ($table_exists) {
        // Test 3: Check table structure
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $response['tests'][] = ['test' => 'Table Structure', 'status' => 'SUCCESS', 'columns' => $columns];
        
        // Test 4: Count users
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $count = $stmt->fetch()['count'];
        $response['tests'][] = ['test' => 'User Count', 'status' => 'SUCCESS', 'count' => $count];
        
        // Test 5: Test the actual API call
        $_POST['action'] = 'load_all_users';
        ob_start();
        include 'user-api-simple.php';
        $api_output = ob_get_clean();
        
        $api_result = json_decode($api_output, true);
        $response['tests'][] = [
            'test' => 'API Call Test', 
            'status' => $api_result ? 'SUCCESS' : 'FAILED',
            'api_response' => $api_result,
            'raw_output' => $api_output
        ];
    }
    
    $response['overall_status'] = 'COMPLETED';
    
} catch (Exception $e) {
    $response['tests'][] = ['test' => 'Error', 'status' => 'FAILED', 'error' => $e->getMessage()];
    $response['overall_status'] = 'FAILED';
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
