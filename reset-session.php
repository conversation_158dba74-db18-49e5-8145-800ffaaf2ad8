<?php
/**
 * Reset Session Script
 * سكريبت إعادة تعيين الجلسة
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'success' => true,
    'message' => 'Session reset completed',
    'timestamp' => date('Y-m-d H:i:s'),
    'actions_taken' => []
];

// Clear PHP session if exists
if (session_status() === PHP_SESSION_ACTIVE) {
    session_destroy();
    $response['actions_taken'][] = 'PHP session destroyed';
}

// Clear any session cookies
$cookies_cleared = 0;
foreach ($_COOKIE as $cookie_name => $cookie_value) {
    if (strpos($cookie_name, 'PHPSESSID') !== false || 
        strpos($cookie_name, 'session') !== false ||
        strpos($cookie_name, 'user') !== false) {
        setcookie($cookie_name, '', time() - 3600, '/');
        $cookies_cleared++;
    }
}

if ($cookies_cleared > 0) {
    $response['actions_taken'][] = "Cleared {$cookies_cleared} session cookies";
}

$response['actions_taken'][] = 'Session reset instructions sent to client';
$response['client_instructions'] = [
    'Clear localStorage',
    'Clear sessionStorage', 
    'Reload page',
    'Show login form'
];

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>

<script>
// Client-side session cleanup
if (typeof localStorage !== 'undefined') {
    // Clear all localStorage
    localStorage.clear();
    console.log('✅ localStorage cleared');
}

if (typeof sessionStorage !== 'undefined') {
    // Clear all sessionStorage
    sessionStorage.clear();
    console.log('✅ sessionStorage cleared');
}

// Reload the page after a short delay
setTimeout(() => {
    if (typeof window !== 'undefined') {
        window.location.href = '/';
    }
}, 2000);
</script>
