<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Healthcare System Test Dashboard - لوحة اختبار النظام الصحي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .btn.secondary {
            background: #2196F3;
        }
        
        .btn.secondary:hover {
            background: #1976D2;
        }
        
        .status-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-left: 5px solid #ddd;
        }
        
        .status-card.excellent {
            border-left-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .status-card.good {
            border-left-color: #2196F3;
            background: #e3f2fd;
        }
        
        .status-card.warning {
            border-left-color: #FF9800;
            background: #fff3e0;
        }
        
        .status-card.error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        
        .status-card h3 {
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .status-card .value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .test-results {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        
        .test-item.success {
            border-left-color: #4CAF50;
        }
        
        .test-item.error {
            border-left-color: #f44336;
        }
        
        .test-item.warning {
            border-left-color: #FF9800;
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-badge.success {
            background: #4CAF50;
            color: white;
        }
        
        .status-badge.error {
            background: #f44336;
            color: white;
        }
        
        .status-badge.warning {
            background: #FF9800;
            color: white;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .recommendations {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .recommendations h3 {
            color: #1976D2;
            margin-bottom: 15px;
        }
        
        .recommendations ul {
            list-style-type: none;
        }
        
        .recommendations li {
            padding: 5px 0;
            padding-right: 20px;
            position: relative;
        }
        
        .recommendations li:before {
            content: "💡";
            position: absolute;
            right: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Healthcare System Test Dashboard</h1>
            <p>لوحة اختبار شاملة لنظام إدارة المراكز الصحية</p>
        </div>
        
        <div class="content">
            <div class="test-controls">
                <button class="btn" onclick="runComprehensiveTest()">🔍 تشغيل الاختبار الشامل</button>
                <button class="btn secondary" onclick="runQuickTest()">⚡ اختبار سريع</button>
                <a href="https://www.csmanager.online/" class="btn secondary">🏠 الذهاب للموقع</a>
            </div>
            
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <h3>جاري تشغيل الاختبارات الشاملة...</h3>
                <p>يرجى الانتظار، هذا قد يستغرق بضع ثوان</p>
            </div>
            
            <div id="results" style="display: none;">
                <div class="status-overview" id="statusOverview">
                    <!-- Status cards will be populated here -->
                </div>
                
                <div class="test-results" id="testResults">
                    <!-- Test results will be populated here -->
                </div>
                
                <div class="recommendations" id="recommendations" style="display: none;">
                    <h3>📋 التوصيات والخطوات التالية</h3>
                    <ul id="recommendationsList">
                        <!-- Recommendations will be populated here -->
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function runComprehensiveTest() {
            showLoading();
            
            try {
                const response = await fetch('comprehensive-system-test.php', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                displayResults(data);
                
            } catch (error) {
                console.error('Test failed:', error);
                displayError('فشل في تشغيل الاختبار: ' + error.message);
            }
        }
        
        async function runQuickTest() {
            showLoading();
            
            try {
                // Quick test - just check a few critical APIs
                const quickTests = [
                    'user-api-simple.php?action=test',
                    'children-api.php?action=load',
                    'medicines-api.php?action=test'
                ];
                
                const results = await Promise.all(
                    quickTests.map(url => fetch(url).then(r => r.json()))
                );
                
                displayQuickResults(results);
                
            } catch (error) {
                console.error('Quick test failed:', error);
                displayError('فشل في الاختبار السريع: ' + error.message);
            }
        }
        
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
        }
        
        function displayResults(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('results').style.display = 'block';
            
            // Display status overview
            const statusOverview = document.getElementById('statusOverview');
            statusOverview.innerHTML = `
                <div class="status-card ${getStatusClass(data.overall_status)}">
                    <h3>الحالة العامة</h3>
                    <div class="value">${getStatusIcon(data.overall_status)}</div>
                    <div>${data.overall_status}</div>
                </div>
                <div class="status-card ${data.summary.critical_success_rate === '100.0%' ? 'excellent' : 'warning'}">
                    <h3>APIs الأساسية</h3>
                    <div class="value">${data.summary.critical_success_rate}</div>
                    <div>${data.summary.critical_apis_working}</div>
                </div>
                <div class="status-card ${parseFloat(data.summary.overall_success_rate) >= 80 ? 'good' : 'warning'}">
                    <h3>جميع APIs</h3>
                    <div class="value">${data.summary.overall_success_rate}</div>
                    <div>${data.summary.total_apis_working}</div>
                </div>
                <div class="status-card ${data.database_tests.connection.status === 'SUCCESS' ? 'excellent' : 'error'}">
                    <h3>قاعدة البيانات</h3>
                    <div class="value">${data.database_tests.connection.status === 'SUCCESS' ? '✅' : '❌'}</div>
                    <div>${data.database_tests.connection.status}</div>
                </div>
            `;
            
            // Display detailed results
            const testResults = document.getElementById('testResults');
            let resultsHTML = '<div class="test-section"><h3>🔗 اختبار APIs</h3>';
            
            for (const [api, result] of Object.entries(data.api_tests)) {
                const statusClass = result.connectivity_test === 'SUCCESS' ? 'success' : 'error';
                const statusBadge = result.connectivity_test === 'SUCCESS' ? 'success' : 'error';
                
                resultsHTML += `
                    <div class="test-item ${statusClass}">
                        <div>
                            <strong>${result.name}</strong>
                            ${result.critical ? '<span style="color: #f44336;">(أساسي)</span>' : ''}
                            <br><small>${api}</small>
                        </div>
                        <div>
                            <span class="status-badge ${statusBadge}">${result.connectivity_test}</span>
                            <small>${result.response_time}ms</small>
                        </div>
                    </div>
                `;
            }
            
            resultsHTML += '</div>';
            
            // Database tables
            resultsHTML += '<div class="test-section"><h3>🗄️ جداول قاعدة البيانات</h3>';
            for (const [table, info] of Object.entries(data.database_tests.tables.required_tables)) {
                resultsHTML += `
                    <div class="test-item success">
                        <div>
                            <strong>${table}</strong>
                            <br><small>${info.description}</small>
                        </div>
                        <div>
                            <span class="status-badge success">موجود</span>
                            <small>${info.record_count} سجل</small>
                        </div>
                    </div>
                `;
            }
            resultsHTML += '</div>';
            
            testResults.innerHTML = resultsHTML;
            
            // Display recommendations
            if (data.recommendations && data.recommendations.length > 0) {
                const recommendationsDiv = document.getElementById('recommendations');
                const recommendationsList = document.getElementById('recommendationsList');
                
                recommendationsList.innerHTML = data.recommendations
                    .map(rec => `<li>${rec}</li>`)
                    .join('');
                
                recommendationsDiv.style.display = 'block';
            }
        }
        
        function displayQuickResults(results) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('results').style.display = 'block';
            
            const working = results.filter(r => r.success).length;
            const total = results.length;
            const percentage = Math.round((working / total) * 100);
            
            document.getElementById('statusOverview').innerHTML = `
                <div class="status-card ${percentage >= 80 ? 'excellent' : 'warning'}">
                    <h3>اختبار سريع</h3>
                    <div class="value">${percentage}%</div>
                    <div>${working}/${total} APIs تعمل</div>
                </div>
            `;
            
            document.getElementById('testResults').innerHTML = `
                <div class="test-section">
                    <h3>⚡ نتائج الاختبار السريع</h3>
                    <p>تم اختبار ${total} APIs أساسية، ${working} منها تعمل بشكل صحيح.</p>
                    <p><strong>للحصول على تقرير مفصل، استخدم الاختبار الشامل.</strong></p>
                </div>
            `;
        }
        
        function displayError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('results').style.display = 'block';
            
            document.getElementById('testResults').innerHTML = `
                <div class="test-section">
                    <div class="test-item error">
                        <div>
                            <strong>خطأ في الاختبار</strong>
                            <br><small>${message}</small>
                        </div>
                        <div>
                            <span class="status-badge error">فشل</span>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function getStatusClass(status) {
            switch (status) {
                case 'EXCELLENT': return 'excellent';
                case 'GOOD': return 'good';
                case 'NEEDS_ATTENTION': return 'warning';
                default: return 'error';
            }
        }
        
        function getStatusIcon(status) {
            switch (status) {
                case 'EXCELLENT': return '🟢';
                case 'GOOD': return '🟡';
                case 'NEEDS_ATTENTION': return '🟠';
                default: return '🔴';
            }
        }
        
        // Auto-run quick test on page load
        window.addEventListener('load', () => {
            setTimeout(runQuickTest, 1000);
        });
    </script>
</body>
</html>
