# نظام إدارة المراكز الصحية - Healthcare Centers Management System

نظام شامل لإدارة المراكز الصحية وتتبع التلقيحات والأدوية ووسائل منع الحمل.

## المميزات الرئيسية

### 🏥 إدارة المراكز الصحية
- إدارة متعددة المراكز
- نظام صلاحيات متدرج (مدير، مشرف، ممرض)
- تتبع المستخدمين والأنشطة

### 👶 إدارة الأطفال
- تسجيل بيانات الأطفال الكاملة
- تتبع التلقيحات حسب العمر
- جدولة التلقيحات التلقائية
- تقارير شاملة عن حالة التلقيحات

### 💉 إدارة اللقاحات
- قائمة شاملة باللقاحات المعتمدة
- تتبع المخزون وتواريخ الانتهاء
- سجل استخدام اللقاحات
- تنبيهات المخزون المنخفض

### 💊 إدارة الأدوية
- كتالوج الأدوية المتاحة
- إدارة المخزون والدفعات
- تتبع تواريخ الانتهاء
- التخطيط الشهري للاحتياجات

### 👨‍⚕️ إدارة المستخدمين
- نظام مصادقة آمن
- إدارة الصلاحيات
- تتبع النشاطات
- إعدادات شخصية

## التقنيات المستخدمة

### الواجهة الأمامية (Frontend)
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript (ES6+)** - التفاعل والديناميكية
- **Font Awesome** - الأيقونات
- **Responsive Design** - متوافق مع جميع الأجهزة

### الواجهة الخلفية (Backend)
- **PHP 7.4+** - لغة البرمجة الخلفية
- **MySQL 5.7+** - قاعدة البيانات
- **RESTful API** - واجهة برمجة التطبيقات
- **PDO** - للاتصال الآمن بقاعدة البيانات

### الأمان
- **Password Hashing** - تشفير كلمات المرور
- **SQL Injection Protection** - حماية من حقن SQL
- **XSS Protection** - حماية من البرمجة النصية المتقاطعة
- **CSRF Protection** - حماية من التزوير
- **Session Management** - إدارة الجلسات الآمنة

## متطلبات النظام

### الخادم
- **PHP 7.4** أو أحدث
- **MySQL 5.7** أو أحدث
- **Apache 2.4** مع mod_rewrite
- **SSL Certificate** (موصى به للإنتاج)

### المتصفح
- **Chrome 80+**
- **Firefox 75+**
- **Safari 13+**
- **Edge 80+**

## التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/healthcare-management.git
cd healthcare-management
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE healthcare_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تشغيل سكريبت إنشاء الجداول
mysql -u username -p healthcare_management < database_design.sql
```

### 3. إعداد الاتصال بقاعدة البيانات
قم بتعديل ملف `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'healthcare_management';
private $username = 'your_username';
private $password = 'your_password';
```

### 4. إعداد صلاحيات المجلدات
```bash
chmod 755 api/
chmod 755 config/
chmod 777 uploads/ (إذا كان موجوداً)
chmod 777 backups/ (إذا كان موجوداً)
```

### 5. إعداد Apache
تأكد من تفعيل mod_rewrite:
```bash
a2enmod rewrite
systemctl restart apache2
```

## الاستخدام

### تسجيل الدخول الافتراضي
- **المدير**: `admin` / `password`
- **الممرض**: `nurse1` / `password`

### الواجهات المتاحة
- **النسخة الجديدة (API)**: `cs-manager-api.html`
- **النسخة القديمة**: `cs-manager.html`

## هيكل المشروع

```
healthcare-management/
├── api/                    # واجهة برمجة التطبيقات
│   ├── index.php          # نقطة دخول API
│   └── classes/           # فئات PHP
├── config/                # ملفات الإعداد
│   └── database.php       # إعداد قاعدة البيانات
├── js/                    # ملفات JavaScript
│   ├── api-client.js      # عميل API
│   └── app-migration.js   # ملف الترحيل
├── css/                   # ملفات التنسيق (إذا كانت منفصلة)
├── uploads/               # ملفات المرفوعات
├── backups/               # النسخ الاحتياطية
├── .htaccess             # إعدادات Apache
├── database_design.sql   # سكريبت قاعدة البيانات
├── cs-manager-api.html   # الواجهة الجديدة
├── cs-manager.html       # الواجهة القديمة
└── README.md             # هذا الملف
```

## API Documentation

### المصادقة
```javascript
// تسجيل الدخول
POST /api/auth/login
{
    "username": "admin",
    "password": "password"
}

// تسجيل الخروج
POST /api/auth/logout

// فحص حالة المصادقة
GET /api/auth/check
```

### إدارة الأطفال
```javascript
// الحصول على قائمة الأطفال
GET /api/children/list?page=1&limit=20&search=

// إضافة طفل جديد
POST /api/children/create
{
    "name": "اسم الطفل",
    "birth_date": "2023-01-01",
    "gender": "male",
    "parent_name": "اسم الوالد",
    "parent_phone": "0123456789",
    "address": "العنوان"
}

// تحديث بيانات طفل
PUT /api/children/update/{id}

// حذف طفل
DELETE /api/children/delete/{id}
```

### إدارة اللقاحات
```javascript
// الحصول على قائمة اللقاحات
GET /api/vaccines/list

// تحديث مخزون لقاح
POST /api/vaccines/stock/update
{
    "vaccine_id": "bcg",
    "quantity": 100,
    "expiry_date": "2024-12-31",
    "batch_number": "BATCH001"
}
```

## الأمان والحماية

### كلمات المرور
- يتم تشفير كلمات المرور باستخدام `password_hash()`
- الحد الأدنى لقوة كلمة المرور: 8 أحرف
- يُنصح بتضمين أحرف كبيرة وصغيرة وأرقام ورموز

### حماية قاعدة البيانات
- استخدام Prepared Statements
- تشفير البيانات الحساسة
- نسخ احتياطية دورية

### حماية الجلسات
- انتهاء صلاحية الجلسة بعد ساعة من عدم النشاط
- تجديد معرف الجلسة عند تسجيل الدخول
- حماية من session hijacking

## النسخ الاحتياطية

### تلقائية
```php
// إنشاء نسخة احتياطية
$database = new Database();
$backup_path = $database->createBackup();
```

### يدوية
```bash
mysqldump -u username -p healthcare_management > backup_$(date +%Y%m%d_%H%M%S).sql
```

## الصيانة والتحديث

### تحديث قاعدة البيانات
```sql
-- إضافة عمود جديد
ALTER TABLE children ADD COLUMN new_field VARCHAR(255);

-- تحديث البيانات
UPDATE children SET new_field = 'default_value';
```

### تنظيف البيانات القديمة
```php
// حذف الإشعارات القديمة
$database->cleanOldData(30); // 30 يوم
```

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
- تحقق من بيانات الاتصال في `config/database.php`
- تأكد من تشغيل خدمة MySQL
- تحقق من صلاحيات المستخدم

#### خطأ 500 Internal Server Error
- فحص ملف error.log
- تحقق من صلاحيات الملفات
- تأكد من تفعيل mod_rewrite

#### مشاكل في تسجيل الدخول
- تحقق من صحة بيانات المستخدم
- فحص جدول users في قاعدة البيانات
- تأكد من تشفير كلمة المرور

## المساهمة في التطوير

### إرشادات المساهمة
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

### معايير الكود
- استخدام PSR-4 للـ PHP
- تعليقات باللغة العربية والإنجليزية
- اختبار الكود قبل الإرسال

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق

## التحديثات المستقبلية

### الإصدار القادم (v2.0)
- [ ] تطبيق الهاتف المحمول
- [ ] تقارير متقدمة
- [ ] تكامل مع أنظمة خارجية
- [ ] إشعارات SMS
- [ ] لوحة تحكم متقدمة

---

**تم تطوير هذا النظام لخدمة المراكز الصحية وتحسين جودة الرعاية الصحية للأطفال.**
