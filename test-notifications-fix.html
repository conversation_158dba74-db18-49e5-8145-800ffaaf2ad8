<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔕 اختبار إصلاح الإشعارات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #28a745; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .stock-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stock-item h5 { margin: 0 0 10px 0; color: #28a745; }
        .stock-input { 
            width: 80px; padding: 8px; margin: 0 10px;
            border: 2px solid #ced4da; border-radius: 4px; text-align: center;
        }
        .month-name-input { 
            width: 200px; padding: 10px; margin: 10px 0;
            border: 2px solid #ced4da; border-radius: 5px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.save { background: #28a745; }
        button.save:hover { background: #218838; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .notifications-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .notification-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 4px solid #007bff;
        }
        .notification-success { border-left-color: #28a745; background: #d4edda; }
        .notification-error { border-left-color: #dc3545; background: #f8d7da; }
        .notification-warning { border-left-color: #ffc107; background: #fff3cd; }
        .grid-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .month-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>🔕 اختبار إصلاح الإشعارات</h1>
    
    <div class="test-section">
        <h2>📋 تعليمات الاختبار</h2>
        <p><strong>الهدف:</strong> التأكد من أن الحفظ التلقائي يتم بصمت، والإشعارات تظهر فقط عند الضغط على زر الحفظ</p>
        <ol>
            <li>أدخل كميات اللقاحات في الحقول أدناه</li>
            <li>لاحظ أنه <strong>لا تظهر إشعارات</strong> عند التغيير</li>
            <li>اضغط زر "💾 حفظ التخطيط الشهري"</li>
            <li>يجب أن يظهر إشعار واحد فقط: "تم حفظ التخطيط الشهري بنجاح"</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>🎯 أدوات التحكم</h2>
        <button onclick="clearNotifications()">🗑️ مسح سجل الإشعارات</button>
        <button onclick="testAutoSave()">🔄 اختبار الحفظ التلقائي</button>
        <button onclick="testManualSave()" class="save">💾 حفظ التخطيط الشهري</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار الإشعارات
    </div>
    
    <div class="grid-container">
        <div>
            <div class="test-section">
                <h3>📝 التخطيط الشهري</h3>
                
                <div class="month-section">
                    <h4>الشهر الأول</h4>
                    <input type="text" class="month-name-input" id="month1Name" 
                           placeholder="اسم الشهر الأول" 
                           onchange="updateMonthName('month1')" 
                           onblur="updateMonthName('month1')">
                    <div id="month1Grid"></div>
                </div>
                
                <div class="month-section">
                    <h4>الشهر الثاني</h4>
                    <input type="text" class="month-name-input" id="month2Name" 
                           placeholder="اسم الشهر الثاني"
                           onchange="updateMonthName('month2')" 
                           onblur="updateMonthName('month2')">
                    <div id="month2Grid"></div>
                </div>
            </div>
        </div>
        
        <div>
            <div class="test-section">
                <h3>🔔 سجل الإشعارات</h3>
                <div id="notificationsLog" class="notifications-log">
                    <div class="notification-item">لم تظهر أي إشعارات بعد...</div>
                </div>
                
                <h4>📊 إحصائيات الإشعارات:</h4>
                <div id="notificationStats">
                    <p>إجمالي الإشعارات: <span id="totalNotifications">0</span></p>
                    <p>إشعارات النجاح: <span id="successNotifications">0</span></p>
                    <p>إشعارات الخطأ: <span id="errorNotifications">0</span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let monthlyPlanning = {};
        let currentUser = { id: 'test_notifications_user', center_id: '1' };
        let notificationCount = { total: 0, success: 0, error: 0, warning: 0 };
        
        const vaccineDefinitions = {
            'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B' },
            'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)' },
            'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)' },
            'Penta': { name: 'الخماسي', frenchName: 'Pentavalent' },
            'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)' }
        };
        
        // تخصيص دالة showToast لتسجيل الإشعارات
        function showToast(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const log = document.getElementById('notificationsLog');
            
            const notification = document.createElement('div');
            notification.className = `notification-item notification-${type}`;
            notification.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            
            log.appendChild(notification);
            log.scrollTop = log.scrollHeight;
            
            // تحديث الإحصائيات
            notificationCount.total++;
            if (type === 'success') notificationCount.success++;
            else if (type === 'error') notificationCount.error++;
            else if (type === 'warning') notificationCount.warning++;
            
            updateNotificationStats();
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateNotificationStats() {
            document.getElementById('totalNotifications').textContent = notificationCount.total;
            document.getElementById('successNotifications').textContent = notificationCount.success;
            document.getElementById('errorNotifications').textContent = notificationCount.error;
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
        }
        
        function initializeData() {
            const defaultVaccines = {};
            Object.keys(vaccineDefinitions).forEach(key => {
                defaultVaccines[key] = 0;
            });
            
            monthlyPlanning = {
                month1: { name: '', vaccines: {...defaultVaccines} },
                month2: { name: '', vaccines: {...defaultVaccines} }
            };
            
            updateDisplay();
        }
        
        function updateDisplay() {
            ['month1', 'month2'].forEach(month => {
                const grid = document.getElementById(`${month}Grid`);
                grid.innerHTML = '';
                
                Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                    const vaccine = vaccineDefinitions[vaccineKey];
                    const currentValue = monthlyPlanning[month].vaccines[vaccineKey] || 0;
                    
                    const vaccineItem = document.createElement('div');
                    vaccineItem.className = 'stock-item';
                    vaccineItem.innerHTML = `
                        <h5>${vaccine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; margin-bottom: 8px;">
                            ${vaccine.frenchName}
                        </div>
                        <div>
                            <input type="number" class="stock-input" id="${month}_${vaccineKey}"
                                   value="${currentValue}" min="0" max="1000"
                                   onchange="updateVaccine('${month}', '${vaccineKey}')"
                                   onblur="updateVaccine('${month}', '${vaccineKey}')">
                            <span>قارورة</span>
                        </div>
                    `;
                    grid.appendChild(vaccineItem);
                });
            });
        }
        
        // محاكاة الحفظ التلقائي (صامت)
        async function updateVaccine(month, vaccineKey) {
            const input = document.getElementById(`${month}_${vaccineKey}`);
            const value = parseInt(input.value) || 0;
            
            monthlyPlanning[month].vaccines[vaccineKey] = value;
            
            // محاكاة الحفظ التلقائي بصمت
            console.log(`🔄 حفظ تلقائي صامت: ${vaccineKey} في ${month} = ${value}`);
            updateStatus(`تم تحديث ${vaccineKey} في ${month}: ${value} (حفظ صامت)`, 'info');
        }
        
        // محاكاة تحديث اسم الشهر (صامت)
        async function updateMonthName(month) {
            const input = document.getElementById(`${month}Name`);
            const monthName = input.value.trim();
            
            monthlyPlanning[month].name = monthName;
            
            // محاكاة الحفظ التلقائي بصمت
            console.log(`🔄 حفظ تلقائي صامت: اسم ${month} = "${monthName}"`);
            updateStatus(`تم تحديث اسم ${month}: "${monthName}" (حفظ صامت)`, 'info');
        }
        
        // محاكاة الحفظ اليدوي (مع إشعار)
        async function testManualSave() {
            updateStatus('جاري حفظ التخطيط الشهري...', 'info');
            
            // محاكاة تأخير الشبكة
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // عرض إشعار النجاح
            showToast('تم حفظ التخطيط الشهري بنجاح', 'success');
            updateStatus('تم حفظ التخطيط الشهري بنجاح', 'pass');
        }
        
        function testAutoSave() {
            updateStatus('اختبار الحفظ التلقائي...', 'info');
            
            // تغيير بعض القيم لمحاكاة الحفظ التلقائي
            document.getElementById('month1_HB1').value = Math.floor(Math.random() * 100);
            document.getElementById('month1_BCG').value = Math.floor(Math.random() * 100);
            
            updateVaccine('month1', 'HB1');
            updateVaccine('month1', 'BCG');
            
            updateStatus('تم اختبار الحفظ التلقائي - لا يجب أن تظهر إشعارات', 'warning');
        }
        
        function clearNotifications() {
            document.getElementById('notificationsLog').innerHTML = '<div class="notification-item">تم مسح سجل الإشعارات...</div>';
            notificationCount = { total: 0, success: 0, error: 0, warning: 0 };
            updateNotificationStats();
            updateStatus('تم مسح سجل الإشعارات', 'info');
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            initializeData();
            updateStatus('جاهز لاختبار الإشعارات - جرب تغيير القيم', 'info');
        };
    </script>
</body>
</html>
