<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Clear Session - مسح الجلسة بالقوة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 600px;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: #4299e1;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        .btn:hover {
            background: #3182ce;
        }
        .btn-danger {
            background: #f56565;
        }
        .btn-danger:hover {
            background: #e53e3e;
        }
        .btn-success {
            background: #48bb78;
        }
        .btn-success:hover {
            background: #38a169;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .status.success {
            background: #c6f6d5;
            color: #22543d;
            border: 2px solid #48bb78;
        }
        .status.info {
            background: #bee3f8;
            color: #2a4365;
            border: 2px solid #4299e1;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #48bb78);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 مسح الجلسة بالقوة</h1>
        <p>هذه الأداة ستقوم بمسح جميع بيانات الجلسة والتخزين المحلي لحل مشكلة "المستخدم غير موجود"</p>
        
        <div id="status" class="status info">
            جاهز لبدء عملية المسح
        </div>
        
        <div class="progress">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        
        <button class="btn btn-danger" onclick="forceClearAll()">🗑️ مسح جميع البيانات</button>
        <button class="btn" onclick="testAndClear()">🔍 فحص ومسح</button>
        <button class="btn btn-success" onclick="goToWebsite()">🏠 الذهاب للموقع</button>
        
        <div style="margin-top: 30px; font-size: 14px; color: #666;">
            <p><strong>ما سيتم مسحه:</strong></p>
            <ul style="text-align: right;">
                <li>localStorage (جميع البيانات المحلية)</li>
                <li>sessionStorage (بيانات الجلسة)</li>
                <li>IndexedDB (قواعد البيانات المحلية)</li>
                <li>WebSQL (إن وجد)</li>
                <li>Cookies (ملفات تعريف الارتباط)</li>
                <li>Cache Storage (التخزين المؤقت)</li>
            </ul>
        </div>
    </div>

    <script>
        let progress = 0;
        
        function updateProgress(percent, message) {
            progress = percent;
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('status').textContent = message;
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
        }
        
        async function forceClearAll() {
            updateStatus('🚀 بدء عملية المسح الشامل...', 'info');
            updateProgress(10, 'مسح localStorage...');
            
            try {
                // Clear localStorage
                if (typeof localStorage !== 'undefined') {
                    localStorage.clear();
                    console.log('✅ localStorage cleared');
                }
                updateProgress(25, 'مسح sessionStorage...');
                
                // Clear sessionStorage
                if (typeof sessionStorage !== 'undefined') {
                    sessionStorage.clear();
                    console.log('✅ sessionStorage cleared');
                }
                updateProgress(40, 'مسح IndexedDB...');
                
                // Clear IndexedDB
                if ('indexedDB' in window) {
                    try {
                        const databases = await indexedDB.databases();
                        await Promise.all(databases.map(db => {
                            return new Promise((resolve, reject) => {
                                const deleteReq = indexedDB.deleteDatabase(db.name);
                                deleteReq.onsuccess = () => resolve();
                                deleteReq.onerror = () => resolve(); // Continue even if fails
                            });
                        }));
                        console.log('✅ IndexedDB cleared');
                    } catch (e) {
                        console.log('⚠️ IndexedDB clear failed:', e);
                    }
                }
                updateProgress(60, 'مسح Cookies...');
                
                // Clear cookies
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                console.log('✅ Cookies cleared');
                updateProgress(80, 'مسح Cache Storage...');
                
                // Clear cache storage
                if ('caches' in window) {
                    try {
                        const cacheNames = await caches.keys();
                        await Promise.all(cacheNames.map(name => caches.delete(name)));
                        console.log('✅ Cache storage cleared');
                    } catch (e) {
                        console.log('⚠️ Cache clear failed:', e);
                    }
                }
                updateProgress(100, 'تم المسح بنجاح!');
                
                updateStatus('✅ تم مسح جميع البيانات بنجاح! سيتم إعادة التوجيه خلال 3 ثوان...', 'success');
                
                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
                
            } catch (error) {
                updateStatus('❌ حدث خطأ: ' + error.message, 'error');
                console.error('Clear error:', error);
            }
        }
        
        async function testAndClear() {
            updateStatus('🔍 فحص البيانات المخزنة...', 'info');
            
            const report = {
                localStorage: localStorage.length,
                sessionStorage: sessionStorage.length,
                cookies: document.cookie.split(';').length,
                userAgent: navigator.userAgent
            };
            
            console.log('📊 تقرير البيانات:', report);
            
            if (report.localStorage > 0 || report.sessionStorage > 0) {
                updateStatus(`🔍 تم العثور على ${report.localStorage} عنصر في localStorage و ${report.sessionStorage} في sessionStorage. مسح...`, 'info');
                await forceClearAll();
            } else {
                updateStatus('✅ لا توجد بيانات مخزنة للمسح', 'success');
            }
        }
        
        function goToWebsite() {
            window.location.href = '/';
        }
        
        // Auto-check on page load
        window.addEventListener('load', () => {
            const hasData = localStorage.length > 0 || sessionStorage.length > 0;
            if (hasData) {
                updateStatus(`⚠️ تم العثور على ${localStorage.length + sessionStorage.length} عنصر مخزن. يُنصح بالمسح.`, 'info');
            } else {
                updateStatus('✅ لا توجد بيانات مخزنة حالياً', 'success');
            }
        });
    </script>
</body>
</html>
