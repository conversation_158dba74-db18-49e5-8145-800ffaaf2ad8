<?php
/**
 * Complete Database Recreation Script
 * سكريبت إعادة إنشاء قاعدة البيانات الكاملة
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// Get parameters
$action = $_GET['action'] ?? 'check';
$root_password = $_GET['root_password'] ?? '';
$force = $_GET['force'] ?? 'false';

$response = [
    'success' => false,
    'message' => '',
    'action' => $action,
    'timestamp' => date('Y-m-d H:i:s'),
    'steps' => []
];

function addStep($message, $success = true) {
    global $response;
    $response['steps'][] = [
        'message' => $message,
        'success' => $success,
        'timestamp' => date('H:i:s')
    ];
}

try {
    switch ($action) {
        case 'check':
            addStep('Database recreation script ready');
            $response['success'] = true;
            $response['message'] = 'Ready to recreate database';
            $response['instructions'] = [
                'To recreate database: recreate-database.php?action=recreate&root_password=YOUR_ROOT_PASSWORD',
                'To force recreate (delete existing): recreate-database.php?action=recreate&root_password=YOUR_ROOT_PASSWORD&force=true',
                'To test connection: recreate-database.php?action=test'
            ];
            break;
            
        case 'test':
            // Test current connection
            require_once 'config/database-live.php';
            try {
                $pdo = getDatabaseConnection();
                $stmt = $pdo->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'csdb'");
                $result = $stmt->fetch();
                addStep("✅ Connection successful - Found {$result['table_count']} tables");
                $response['success'] = true;
                $response['message'] = 'Database connection is working';
            } catch (Exception $e) {
                addStep("❌ Connection failed: " . $e->getMessage(), false);
                $response['message'] = 'Database connection failed';
            }
            break;
            
        case 'recreate':
            if (empty($root_password)) {
                throw new Exception('Root password is required for database recreation');
            }
            
            addStep('🚀 Starting database recreation process...');
            
            // Connect as root
            $root_pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', $root_password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            ]);
            addStep('✅ Connected as root user');
            
            // Drop database if force is enabled
            if ($force === 'true') {
                try {
                    $root_pdo->exec("DROP DATABASE IF EXISTS `csdb`");
                    addStep('🗑️ Dropped existing database (force mode)');
                } catch (PDOException $e) {
                    addStep('⚠️ Could not drop database: ' . $e->getMessage());
                }
            }
            
            // Create database
            $root_pdo->exec("CREATE DATABASE IF NOT EXISTS `csdb` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            addStep('✅ Created database: csdb');
            
            // Create/recreate user
            try {
                $root_pdo->exec("DROP USER IF EXISTS 'csdbuser'@'localhost'");
                addStep('🗑️ Removed existing user');
            } catch (PDOException $e) {
                // User didn't exist, that's fine
            }
            
            $root_pdo->exec("CREATE USER 'csdbuser'@'localhost' IDENTIFIED BY 'j5aKN6lz5bsujTcWaYAd'");
            addStep('✅ Created user: csdbuser');
            
            $root_pdo->exec("GRANT ALL PRIVILEGES ON `csdb`.* TO 'csdbuser'@'localhost'");
            $root_pdo->exec("FLUSH PRIVILEGES");
            addStep('✅ Granted permissions to csdbuser');
            
            // Switch to the new database
            $root_pdo->exec("USE `csdb`");
            addStep('✅ Switched to csdb database');
            
            // Read and execute the database schema
            $schema_file = __DIR__ . '/database_design.sql';
            if (!file_exists($schema_file)) {
                throw new Exception('Database schema file not found: ' . $schema_file);
            }
            
            $schema_sql = file_get_contents($schema_file);
            addStep('✅ Read database schema file');
            
            // Replace database name in schema
            $schema_sql = str_replace('healthcare_management', 'csdb', $schema_sql);
            $schema_sql = str_replace('CREATE DATABASE IF NOT EXISTS healthcare_management', 'CREATE DATABASE IF NOT EXISTS csdb', $schema_sql);
            $schema_sql = str_replace('USE healthcare_management;', 'USE csdb;', $schema_sql);
            
            // Split into individual queries
            $queries = explode(';', $schema_sql);
            $executed_queries = 0;
            
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query) && !preg_match('/^(CREATE DATABASE|USE)/i', $query)) {
                    try {
                        $root_pdo->exec($query);
                        $executed_queries++;
                    } catch (PDOException $e) {
                        addStep("⚠️ Query warning: " . substr($query, 0, 50) . "... - " . $e->getMessage());
                    }
                }
            }
            
            addStep("✅ Executed {$executed_queries} database queries");
            
            // Test the new connection
            $test_pdo = new PDO("mysql:host=localhost;dbname=csdb;charset=utf8mb4", 'csdbuser', 'j5aKN6lz5bsujTcWaYAd', [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            ]);
            
            // Count tables
            $stmt = $test_pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            addStep("✅ Database recreation complete - Created " . count($tables) . " tables");
            
            // List created tables
            if (count($tables) > 0) {
                addStep("📋 Created tables: " . implode(', ', $tables));
            }
            
            $response['success'] = true;
            $response['message'] = 'Database recreated successfully';
            $response['tables_created'] = count($tables);
            $response['table_list'] = $tables;
            
            break;
            
        default:
            throw new Exception('Invalid action. Use: check, test, or recreate');
    }
    
} catch (Exception $e) {
    addStep("❌ Error: " . $e->getMessage(), false);
    $response['message'] = $e->getMessage();
    $response['error'] = true;
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
