<?php
/**
 * Debug User Loading Issues
 * تشخيص مشاكل تحميل المستخدمين
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'debug_info' => []
];

try {
    // Test 1: Check database connection
    require_once 'config/database-live.php';
    $pdo = getDatabaseConnection();
    $response['debug_info'][] = ['test' => 'Database Connection', 'status' => 'SUCCESS'];
    
    // Test 2: Check users table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
    $active_users = $stmt->fetch()['count'];
    $response['debug_info'][] = ['test' => 'Active Users Count', 'result' => $active_users];
    
    // Test 3: Get all users with details
    $stmt = $pdo->query("SELECT id, username, name, role, is_active FROM users ORDER BY created_at DESC");
    $all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $response['debug_info'][] = ['test' => 'All Users', 'result' => $all_users];
    
    // Test 4: Test the user API directly
    $test_api_data = [
        'action' => 'load_all_users'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://www.csmanager.online/user-api-simple.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_api_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $api_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        $response['debug_info'][] = ['test' => 'User API Call', 'status' => 'ERROR', 'error' => $curl_error];
    } else {
        $api_data = json_decode($api_result, true);
        $response['debug_info'][] = ['test' => 'User API Call', 'status' => 'SUCCESS', 'result' => $api_data];
    }
    
    // Test 5: Check for any localStorage remnants (simulate what browser might have)
    $response['debug_info'][] = [
        'test' => 'Potential Issues',
        'suggestions' => [
            'Clear browser cache completely',
            'Try incognito/private browsing mode',
            'Check if any browser extensions are interfering',
            'Verify no cached service workers'
        ]
    ];
    
    $response['success'] = true;
    $response['summary'] = [
        'database_working' => true,
        'users_available' => $active_users > 0,
        'api_accessible' => !$curl_error,
        'recommended_action' => $active_users > 0 ? 'Try hard refresh (Ctrl+F5) or incognito mode' : 'Create sample users'
    ];
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['error'] = $e->getMessage();
    $response['debug_info'][] = ['test' => 'Error', 'status' => 'FAILED', 'error' => $e->getMessage()];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
