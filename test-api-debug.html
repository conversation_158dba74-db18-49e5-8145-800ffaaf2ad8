<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 اختبار API وإصلاح الأخطاء</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .error-section { border-color: #dc3545; }
        .success-section { border-color: #28a745; }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.test { background: #17a2b8; }
        button.test:hover { background: #138496; }
        button.fix { background: #28a745; }
        button.fix:hover { background: #218838; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { 
            background: #f8f9fa; padding: 15px; border-radius: 5px; 
            overflow-x: auto; font-size: 12px; white-space: pre-wrap;
            max-height: 300px; overflow-y: auto;
        }
        .log-entry {
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .log-error { border-left-color: #dc3545; background: #f8d7da; }
        .log-success { border-left-color: #28a745; background: #d4edda; }
        .log-warning { border-left-color: #ffc107; background: #fff3cd; }
    </style>
</head>
<body>
    <h1>🔧 اختبار API وإصلاح خطأ 500</h1>
    
    <div class="test-section error-section">
        <h2>❌ المشكلة الحالية</h2>
        <p><strong>خطأ 500:</strong> vaccines-api.php يعطي خطأ خادم داخلي</p>
        <p><strong>السبب المحتمل:</strong> مشكلة في دالة createMedicineTables أو updateVaccineStock</p>
        <p><strong>الحل:</strong> اختبار كل دالة على حدة وإصلاح الأخطاء</p>
    </div>
    
    <div class="test-section">
        <h2>🧪 اختبارات API</h2>
        <button onclick="testBasicAPI()" class="test">🔍 اختبار API الأساسي</button>
        <button onclick="testVaccineStock()" class="test">💉 اختبار مخزون اللقاحات</button>
        <button onclick="testMedicineAPI()" class="test">💊 اختبار API الأدوية</button>
        <button onclick="testDatabaseConnection()" class="test">🗄️ اختبار اتصال قاعدة البيانات</button>
        <button onclick="clearLogs()">🗑️ مسح السجلات</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار API
    </div>
    
    <div class="test-section">
        <h2>📋 سجل الاختبارات</h2>
        <div id="testLogs"></div>
    </div>
    
    <div class="test-section">
        <h2>📥 آخر استجابة من الخادم</h2>
        <pre id="lastResponse">لا توجد استجابة بعد</pre>
    </div>

    <script>
        let testCount = 0;
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function addLog(message, type = 'info') {
            testCount++;
            const logs = document.getElementById('testLogs');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<strong>[${testCount}] [${timestamp}]</strong> ${message}`;
            
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
            
            console.log(`[${testCount}] ${message}`);
        }
        
        function showResponse(response) {
            document.getElementById('lastResponse').textContent = JSON.stringify(response, null, 2);
        }
        
        async function testBasicAPI() {
            updateStatus('جاري اختبار API الأساسي...', 'info');
            addLog('بدء اختبار API الأساسي', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'test',
                        user_id: 'test_user'
                    })
                });
                
                addLog(`استجابة HTTP: ${response.status} ${response.statusText}`, 
                       response.ok ? 'success' : 'error');
                
                const responseText = await response.text();
                addLog(`نص الاستجابة: ${responseText.substring(0, 200)}...`, 'info');
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        showResponse(data);
                        addLog('✅ API يعمل بشكل أساسي', 'success');
                        updateStatus('API يعمل بشكل أساسي', 'pass');
                    } catch (e) {
                        addLog('❌ استجابة غير صالحة JSON: ' + e.message, 'error');
                        showResponse({ error: 'Invalid JSON', response: responseText });
                        updateStatus('استجابة غير صالحة من API', 'fail');
                    }
                } else {
                    addLog(`❌ خطأ HTTP ${response.status}`, 'error');
                    showResponse({ error: `HTTP ${response.status}`, response: responseText });
                    updateStatus(`خطأ HTTP ${response.status}`, 'fail');
                }
                
            } catch (error) {
                addLog('❌ خطأ في الشبكة: ' + error.message, 'error');
                showResponse({ error: 'Network Error', message: error.message });
                updateStatus('خطأ في الشبكة', 'fail');
            }
        }
        
        async function testVaccineStock() {
            updateStatus('جاري اختبار مخزون اللقاحات...', 'info');
            addLog('بدء اختبار مخزون اللقاحات', 'info');
            
            try {
                const testStock = {
                    'HB1': 10,
                    'BCG': 20,
                    'VPO': 30
                };
                
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'update_stock',
                        user_id: 'test_user',
                        center_id: '1',
                        stock: testStock
                    })
                });
                
                addLog(`استجابة تحديث المخزون: ${response.status}`, 
                       response.ok ? 'success' : 'error');
                
                const responseText = await response.text();
                addLog(`نص الاستجابة: ${responseText}`, 'info');
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        showResponse(data);
                        
                        if (data.success) {
                            addLog('✅ تم تحديث مخزون اللقاحات بنجاح', 'success');
                            updateStatus('مخزون اللقاحات يعمل بشكل صحيح', 'pass');
                        } else {
                            addLog('❌ فشل تحديث المخزون: ' + data.message, 'error');
                            updateStatus('فشل تحديث المخزون', 'fail');
                        }
                    } catch (e) {
                        addLog('❌ استجابة غير صالحة JSON: ' + e.message, 'error');
                        updateStatus('استجابة غير صالحة', 'fail');
                    }
                } else {
                    addLog(`❌ خطأ HTTP ${response.status}`, 'error');
                    showResponse({ error: `HTTP ${response.status}`, response: responseText });
                    updateStatus(`خطأ في تحديث المخزون: ${response.status}`, 'fail');
                }
                
            } catch (error) {
                addLog('❌ خطأ في اختبار المخزون: ' + error.message, 'error');
                updateStatus('خطأ في اختبار المخزون', 'fail');
            }
        }
        
        async function testMedicineAPI() {
            updateStatus('جاري اختبار API الأدوية الجديد...', 'info');
            addLog('بدء اختبار medicines-api.php', 'info');

            try {
                // اختبار إضافة دواء
                const response = await fetch('medicines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'add_medicine',
                        user_id: 'test_user',
                        center_id: '1',
                        name: 'دواء تجريبي',
                        unit: 'قرص'
                    })
                });
                
                addLog(`استجابة إضافة الدواء: ${response.status}`, 
                       response.ok ? 'success' : 'error');
                
                const responseText = await response.text();
                addLog(`نص الاستجابة: ${responseText}`, 'info');
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        showResponse(data);
                        
                        if (data.success) {
                            addLog('✅ تم إضافة الدواء بنجاح', 'success');
                            updateStatus('API الأدوية يعمل بشكل صحيح', 'pass');
                        } else {
                            addLog('❌ فشل إضافة الدواء: ' + data.message, 'error');
                            updateStatus('فشل إضافة الدواء', 'fail');
                        }
                    } catch (e) {
                        addLog('❌ استجابة غير صالحة JSON: ' + e.message, 'error');
                        updateStatus('استجابة غير صالحة', 'fail');
                    }
                } else {
                    addLog(`❌ خطأ HTTP ${response.status}`, 'error');
                    showResponse({ error: `HTTP ${response.status}`, response: responseText });
                    updateStatus(`خطأ في API الأدوية: ${response.status}`, 'fail');
                }
                
            } catch (error) {
                addLog('❌ خطأ في اختبار API الأدوية: ' + error.message, 'error');
                updateStatus('خطأ في اختبار API الأدوية', 'fail');
            }
        }
        
        async function testDatabaseConnection() {
            updateStatus('جاري اختبار اتصال قاعدة البيانات...', 'info');
            addLog('بدء اختبار اتصال قاعدة البيانات', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'load_vaccines',
                        user_id: 'test_user'
                    })
                });
                
                addLog(`استجابة تحميل اللقاحات: ${response.status}`, 
                       response.ok ? 'success' : 'error');
                
                const responseText = await response.text();
                addLog(`نص الاستجابة: ${responseText.substring(0, 300)}...`, 'info');
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        showResponse(data);
                        
                        if (data.success) {
                            addLog('✅ اتصال قاعدة البيانات يعمل بشكل صحيح', 'success');
                            addLog(`📊 تم تحميل ${data.vaccines?.length || 0} لقاح`, 'info');
                            updateStatus('قاعدة البيانات متصلة وتعمل', 'pass');
                        } else {
                            addLog('❌ مشكلة في قاعدة البيانات: ' + data.message, 'error');
                            updateStatus('مشكلة في قاعدة البيانات', 'fail');
                        }
                    } catch (e) {
                        addLog('❌ استجابة غير صالحة JSON: ' + e.message, 'error');
                        updateStatus('استجابة غير صالحة', 'fail');
                    }
                } else {
                    addLog(`❌ خطأ HTTP ${response.status}`, 'error');
                    showResponse({ error: `HTTP ${response.status}`, response: responseText });
                    updateStatus(`خطأ في قاعدة البيانات: ${response.status}`, 'fail');
                }
                
            } catch (error) {
                addLog('❌ خطأ في اختبار قاعدة البيانات: ' + error.message, 'error');
                updateStatus('خطأ في اختبار قاعدة البيانات', 'fail');
            }
        }
        
        function clearLogs() {
            document.getElementById('testLogs').innerHTML = '';
            document.getElementById('lastResponse').textContent = 'تم مسح السجلات';
            testCount = 0;
            updateStatus('تم مسح السجلات', 'info');
        }
        
        // تشغيل اختبار تلقائي عند التحميل
        window.onload = function() {
            updateStatus('جاهز لاختبار API - ابدأ بالاختبار الأساسي', 'info');
            addLog('تم تحميل صفحة اختبار API', 'info');
        };
    </script>
</body>
</html>
