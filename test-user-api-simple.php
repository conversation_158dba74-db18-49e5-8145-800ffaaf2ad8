<?php
/**
 * Simple User API Test
 * اختبار بسيط لـ User API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

echo "Testing User API...\n\n";

// Test 1: Test with action parameter
echo "Test 1: API with action=test\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.csmanager.online/user-api-simple.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['action' => 'test']));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$result1 = curl_exec($ch);
curl_close($ch);
echo "Result: " . $result1 . "\n\n";

// Test 2: Test load_all_users
echo "Test 2: API with action=load_all_users\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.csmanager.online/user-api-simple.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['action' => 'load_all_users']));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$result2 = curl_exec($ch);
curl_close($ch);
echo "Result: " . $result2 . "\n\n";

// Test 3: Direct database check
echo "Test 3: Direct database check\n";
try {
    require_once 'config/database-live.php';
    $pdo = getDatabaseConnection();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $count = $stmt->fetch()['count'];
    echo "Users in database: " . $count . "\n";
    
    if ($count > 0) {
        $stmt = $pdo->query("SELECT id, username, name, role FROM users LIMIT 3");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "Sample users: " . json_encode($users, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

echo "\nTest completed.";
?>
