<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 اختبار أولوية المهام</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .success-section { border-color: #28a745; }
        .error-section { border-color: #dc3545; }
        .warning-section { border-color: #ffc107; }
        .task-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .form-field {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .form-field label {
            width: 120px;
            font-weight: bold;
        }
        .form-field input, .form-field select {
            flex: 1;
            padding: 8px;
            border: 2px solid #ced4da;
            border-radius: 4px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.test { background: #17a2b8; }
        button.test:hover { background: #138496; }
        button.add { background: #28a745; }
        button.add:hover { background: #218838; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { 
            background: #f8f9fa; padding: 15px; border-radius: 5px; 
            overflow-x: auto; font-size: 12px; white-space: pre-wrap;
        }
        .priority-low { background: #d4edda; color: #155724; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-high { background: #f8d7da; color: #721c24; }
        .task-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
    </style>
</head>
<body>
    <h1>📋 اختبار أولوية المهام مع MySQL</h1>
    
    <div class="test-section error-section">
        <h2>❌ المشكلة المكتشفة</h2>
        <p><strong>خطأ 400:</strong> Data truncated for column 'priority' at row 1</p>
        <p><strong>السبب:</strong> قاعدة البيانات تقبل فقط: 'low', 'medium', 'high'</p>
        <p><strong>المشكلة:</strong> الكود كان يرسل 'urgent' وهي غير مدعومة</p>
        <p><strong>الحل:</strong> إزالة 'urgent' واستخدام 'high' بدلاً منها</p>
    </div>
    
    <div class="test-section success-section">
        <h2>✅ الإصلاحات المطبقة</h2>
        <ul>
            <li>✅ إزالة خيار "عاجلة" من قائمة الأولوية</li>
            <li>✅ إضافة تحقق من صحة قيم الأولوية</li>
            <li>✅ تعيين "متوسطة" كقيمة افتراضية</li>
            <li>✅ إصلاح دالة `getPriorityLabel`</li>
            <li>✅ إصلاح فلترة المهام عالية الأولوية</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 اختبار إضافة مهام بأولويات مختلفة</h2>
        <div class="task-form">
            <div class="form-field">
                <label for="taskTitle">عنوان المهمة:</label>
                <input type="text" id="taskTitle" placeholder="أدخل عنوان المهمة">
            </div>
            <div class="form-field">
                <label for="taskDescription">الوصف:</label>
                <input type="text" id="taskDescription" placeholder="وصف المهمة">
            </div>
            <div class="form-field">
                <label for="taskCategory">الفئة:</label>
                <select id="taskCategory">
                    <option value="general">عام</option>
                    <option value="vaccines">لقاحات</option>
                    <option value="medicines">أدوية</option>
                    <option value="family_planning">تنظيم الأسرة</option>
                </select>
            </div>
            <div class="form-field">
                <label for="taskPriority">الأولوية:</label>
                <select id="taskPriority">
                    <option value="low">منخفضة</option>
                    <option value="medium" selected>متوسطة</option>
                    <option value="high">عالية</option>
                </select>
            </div>
            <div class="form-field">
                <label>&nbsp;</label>
                <button onclick="addTestTask()" class="add">➕ إضافة مهمة تجريبية</button>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 أدوات الاختبار</h2>
        <button onclick="testAllPriorities()" class="test">🧪 اختبار جميع الأولويات</button>
        <button onclick="loadTasks()" class="test">📂 تحميل المهام</button>
        <button onclick="clearTasks()">🗑️ مسح المهام التجريبية</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار أولوية المهام
    </div>
    
    <div class="test-section">
        <h2>📋 المهام المضافة</h2>
        <div id="tasksList">لا توجد مهام بعد</div>
    </div>
    
    <div class="test-section">
        <h2>📊 تفاصيل الاختبار</h2>
        <pre id="testResults">لا توجد نتائج بعد</pre>
    </div>

    <script>
        let tasks = [];
        let currentUser = { id: 'priority_test_user', center_id: '1' };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function addTestResult(message) {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            results.textContent += `[${timestamp}] ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }
        
        async function addTestTask() {
            const title = document.getElementById('taskTitle').value.trim();
            const description = document.getElementById('taskDescription').value.trim();
            const category = document.getElementById('taskCategory').value;
            const priority = document.getElementById('taskPriority').value;
            
            if (!title) {
                updateStatus('يرجى إدخال عنوان المهمة', 'fail');
                return;
            }
            
            try {
                updateStatus('جاري إضافة المهمة...', 'info');
                addTestResult(`محاولة إضافة مهمة: "${title}" بأولوية: ${priority}`);
                
                const response = await fetch('tasks-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'add_task',
                        user_id: currentUser.id,
                        center_id: currentUser.center_id,
                        title: title,
                        description: description,
                        category: category,
                        priority: priority,
                        dueDate: '',
                        dueTime: '',
                        notes: 'مهمة تجريبية لاختبار الأولوية'
                    })
                });
                
                const responseText = await response.text();
                addTestResult(`استجابة الخادم: ${response.status} - ${responseText}`);
                
                if (response.ok) {
                    const data = JSON.parse(responseText);
                    if (data.success) {
                        tasks.push(data.task);
                        displayTasks();
                        
                        // مسح الحقول
                        document.getElementById('taskTitle').value = '';
                        document.getElementById('taskDescription').value = '';
                        
                        updateStatus(`✅ تم إضافة المهمة بأولوية "${priority}" بنجاح`, 'pass');
                        addTestResult(`✅ نجح إضافة المهمة بمعرف: ${data.task.id}`);
                    } else {
                        updateStatus('❌ فشل في إضافة المهمة: ' + data.message, 'fail');
                        addTestResult(`❌ فشل: ${data.message}`);
                    }
                } else {
                    updateStatus(`❌ خطأ HTTP ${response.status}`, 'fail');
                    addTestResult(`❌ خطأ HTTP: ${responseText}`);
                }
                
            } catch (error) {
                updateStatus('❌ خطأ في إضافة المهمة: ' + error.message, 'fail');
                addTestResult(`❌ خطأ: ${error.message}`);
            }
        }
        
        async function testAllPriorities() {
            updateStatus('جاري اختبار جميع الأولويات...', 'info');
            addTestResult('=== بدء اختبار جميع الأولويات ===');
            
            const priorities = [
                { value: 'low', name: 'منخفضة' },
                { value: 'medium', name: 'متوسطة' },
                { value: 'high', name: 'عالية' }
            ];
            
            for (let i = 0; i < priorities.length; i++) {
                const priority = priorities[i];
                
                try {
                    addTestResult(`اختبار الأولوية: ${priority.name} (${priority.value})`);
                    
                    const response = await fetch('tasks-api.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            action: 'add_task',
                            user_id: currentUser.id,
                            center_id: currentUser.center_id,
                            title: `مهمة اختبار ${priority.name}`,
                            description: `مهمة لاختبار الأولوية ${priority.name}`,
                            category: 'general',
                            priority: priority.value,
                            dueDate: '',
                            dueTime: '',
                            notes: `اختبار تلقائي للأولوية ${priority.value}`
                        })
                    });
                    
                    const responseText = await response.text();
                    
                    if (response.ok) {
                        const data = JSON.parse(responseText);
                        if (data.success) {
                            tasks.push(data.task);
                            addTestResult(`✅ نجح اختبار ${priority.name}: ${data.task.id}`);
                        } else {
                            addTestResult(`❌ فشل اختبار ${priority.name}: ${data.message}`);
                        }
                    } else {
                        addTestResult(`❌ خطأ HTTP في اختبار ${priority.name}: ${response.status}`);
                    }
                    
                } catch (error) {
                    addTestResult(`❌ خطأ في اختبار ${priority.name}: ${error.message}`);
                }
                
                // انتظار قصير بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            displayTasks();
            addTestResult('=== انتهاء اختبار جميع الأولويات ===');
            updateStatus('تم اختبار جميع الأولويات', 'pass');
        }
        
        async function loadTasks() {
            try {
                updateStatus('جاري تحميل المهام...', 'info');
                
                const response = await fetch('tasks-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'load_tasks',
                        user_id: currentUser.id
                    })
                });
                
                const responseText = await response.text();
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    tasks = data.tasks || [];
                    displayTasks();
                    updateStatus(`تم تحميل ${tasks.length} مهمة`, 'pass');
                    addTestResult(`تم تحميل ${tasks.length} مهمة من قاعدة البيانات`);
                } else {
                    updateStatus('فشل في تحميل المهام', 'fail');
                }
                
            } catch (error) {
                updateStatus('خطأ في تحميل المهام: ' + error.message, 'fail');
            }
        }
        
        function displayTasks() {
            const container = document.getElementById('tasksList');
            
            if (tasks.length === 0) {
                container.innerHTML = '<p>لا توجد مهام بعد</p>';
                return;
            }
            
            let html = '';
            tasks.forEach(task => {
                const priorityClass = `priority-${task.priority}`;
                const priorityText = getPriorityText(task.priority);
                
                html += `
                    <div class="task-item ${priorityClass}">
                        <h4>${task.title}</h4>
                        <p><strong>الوصف:</strong> ${task.description || 'لا يوجد'}</p>
                        <p><strong>الفئة:</strong> ${task.category}</p>
                        <p><strong>الأولوية:</strong> ${priorityText} (${task.priority})</p>
                        <p><strong>المعرف:</strong> ${task.id}</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${task.createdAt}</p>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function getPriorityText(priority) {
            const priorities = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية'
            };
            return priorities[priority] || 'متوسطة';
        }
        
        async function clearTasks() {
            if (!confirm('هل أنت متأكد من مسح جميع المهام التجريبية؟')) return;
            
            try {
                updateStatus('جاري مسح المهام...', 'warning');
                
                for (const task of tasks) {
                    await fetch('tasks-api.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            action: 'delete_task',
                            user_id: currentUser.id,
                            task_id: task.id
                        })
                    });
                }
                
                tasks = [];
                displayTasks();
                updateStatus('تم مسح جميع المهام', 'pass');
                addTestResult('تم مسح جميع المهام التجريبية');
                
            } catch (error) {
                updateStatus('خطأ في مسح المهام: ' + error.message, 'fail');
            }
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            updateStatus('جاهز لاختبار أولوية المهام', 'info');
            addTestResult('تم تحميل صفحة اختبار أولوية المهام');
        };
    </script>
</body>
</html>
