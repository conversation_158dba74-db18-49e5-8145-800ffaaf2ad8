<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔢 اختبار حفظ وتحميل الأرقام</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .stock-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stock-item h5 { margin: 0 0 10px 0; color: #28a745; }
        .stock-input { 
            width: 80px; padding: 8px; margin: 0 10px;
            border: 2px solid #ced4da; border-radius: 4px; text-align: center;
            font-size: 16px; font-weight: bold;
        }
        .stock-input:focus { border-color: #007bff; outline: none; }
        .month-name-input { 
            width: 200px; padding: 10px; margin: 10px 0;
            border: 2px solid #ced4da; border-radius: 5px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.save { background: #28a745; }
        button.save:hover { background: #218838; }
        button.load { background: #17a2b8; }
        button.load:hover { background: #138496; }
        button.clear { background: #dc3545; }
        button.clear:hover { background: #c82333; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { 
            background: #f8f9fa; padding: 15px; border-radius: 5px; 
            overflow-x: auto; font-size: 12px; white-space: pre-wrap;
        }
        .grid-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .month-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: #f8f9fa; }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        .before, .after {
            padding: 10px;
            border-radius: 5px;
        }
        .before { background: #fff3cd; border: 1px solid #ffc107; }
        .after { background: #d4edda; border: 1px solid #28a745; }
    </style>
</head>
<body>
    <h1>🔢 اختبار حفظ وتحميل الأرقام في stock-input</h1>
    
    <div class="test-section">
        <h2>🎯 هدف الاختبار</h2>
        <p>التأكد من أن الأرقام المدخلة في حقول stock-input تُحفظ وتُحمّل بشكل صحيح في:</p>
        <ul>
            <li>✅ localStorage (النسخة الاحتياطية)</li>
            <li>✅ قاعدة البيانات MySQL</li>
            <li>✅ عند فتح متصفح جديد</li>
            <li>✅ عند إعادة تحميل الصفحة</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔧 أدوات التحكم</h2>
        <button onclick="setTestNumbers()">📝 إدخال أرقام تجريبية</button>
        <button onclick="saveToDatabase()" class="save">💾 حفظ في قاعدة البيانات</button>
        <button onclick="loadFromDatabase()" class="load">📂 تحميل من قاعدة البيانات</button>
        <button onclick="clearAllData()" class="clear">🗑️ مسح جميع البيانات</button>
        <button onclick="compareBeforeAfter()">🔍 مقارنة قبل وبعد</button>
        <button onclick="simulateNewBrowser()">🌐 محاكاة متصفح جديد</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار حفظ وتحميل الأرقام
    </div>
    
    <div class="grid-container">
        <div>
            <div class="test-section">
                <h3>📝 إدخال البيانات</h3>
                
                <div class="month-section">
                    <h4>الشهر الأول</h4>
                    <input type="text" class="month-name-input" id="month1Name" 
                           placeholder="اسم الشهر الأول" 
                           onchange="updateMonthName('month1')" 
                           onblur="updateMonthName('month1')">
                    <div id="month1Grid"></div>
                </div>
                
                <div class="month-section">
                    <h4>الشهر الثاني</h4>
                    <input type="text" class="month-name-input" id="month2Name" 
                           placeholder="اسم الشهر الثاني"
                           onchange="updateMonthName('month2')" 
                           onblur="updateMonthName('month2')">
                    <div id="month2Grid"></div>
                </div>
            </div>
        </div>
        
        <div>
            <div class="test-section">
                <h3>📊 مقارنة البيانات</h3>
                <div id="comparisonResults">
                    <p>لم يتم تشغيل مقارنة بعد...</p>
                </div>
                
                <h4>📝 البيانات الحالية:</h4>
                <pre id="currentData">لا توجد بيانات</pre>
                
                <h4>💾 البيانات المحفوظة:</h4>
                <pre id="savedData">لا توجد بيانات محفوظة</pre>
            </div>
        </div>
    </div>

    <script>
        let monthlyPlanning = {};
        let currentUser = { id: 'test_numbers_user', center_id: '1' };
        let beforeData = {};
        
        const vaccineDefinitions = {
            'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B' },
            'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)' },
            'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)' },
            'Penta': { name: 'الخماسي', frenchName: 'Pentavalent' },
            'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)' }
        };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function initializeData() {
            const defaultVaccines = {};
            Object.keys(vaccineDefinitions).forEach(key => {
                defaultVaccines[key] = 0;
            });
            
            monthlyPlanning = {
                month1: { name: '', vaccines: {...defaultVaccines} },
                month2: { name: '', vaccines: {...defaultVaccines} }
            };
            
            updateDisplay();
            showCurrentData();
        }
        
        function updateDisplay() {
            ['month1', 'month2'].forEach(month => {
                const grid = document.getElementById(`${month}Grid`);
                grid.innerHTML = '';
                
                Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                    const vaccine = vaccineDefinitions[vaccineKey];
                    const currentValue = monthlyPlanning[month].vaccines[vaccineKey] || 0;
                    
                    const vaccineItem = document.createElement('div');
                    vaccineItem.className = 'stock-item';
                    vaccineItem.innerHTML = `
                        <h5>${vaccine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; margin-bottom: 8px;">
                            ${vaccine.frenchName}
                        </div>
                        <div>
                            <input type="number" class="stock-input" id="${month}_${vaccineKey}"
                                   value="${currentValue}" min="0" max="1000"
                                   onchange="updateVaccine('${month}', '${vaccineKey}')"
                                   onblur="updateVaccine('${month}', '${vaccineKey}')">
                            <span>قارورة</span>
                        </div>
                    `;
                    grid.appendChild(vaccineItem);
                });
            });
        }
        
        function updateVaccine(month, vaccineKey) {
            const input = document.getElementById(`${month}_${vaccineKey}`);
            const value = parseInt(input.value) || 0;
            
            console.log(`🔄 تحديث ${month}_${vaccineKey}: ${input.value} -> ${value}`);
            
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].vaccines[vaccineKey] = value;
            
            showCurrentData();
            updateStatus(`تم تحديث ${vaccineKey} في ${month}: ${value}`, 'info');
        }
        
        function updateMonthName(month) {
            const input = document.getElementById(`${month}Name`);
            const monthName = input.value.trim();
            
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].name = monthName;
            
            showCurrentData();
            updateStatus(`تم تحديث اسم ${month}: "${monthName}"`, 'info');
        }
        
        function setTestNumbers() {
            // إدخال أرقام تجريبية محددة
            const testData = {
                month1: {
                    name: 'يناير 2025',
                    vaccines: { HB1: 123, BCG: 456, VPO: 789 }
                },
                month2: {
                    name: 'فبراير 2025',
                    vaccines: { Penta: 321, RR: 654 }
                }
            };
            
            // حفظ البيانات الحالية للمقارنة
            beforeData = JSON.parse(JSON.stringify(monthlyPlanning));
            
            // تطبيق البيانات التجريبية
            monthlyPlanning = testData;
            
            // تحديث الحقول
            document.getElementById('month1Name').value = testData.month1.name;
            document.getElementById('month2Name').value = testData.month2.name;
            
            Object.keys(testData.month1.vaccines).forEach(vaccine => {
                const input = document.getElementById(`month1_${vaccine}`);
                if (input) input.value = testData.month1.vaccines[vaccine];
            });
            
            Object.keys(testData.month2.vaccines).forEach(vaccine => {
                const input = document.getElementById(`month2_${vaccine}`);
                if (input) input.value = testData.month2.vaccines[vaccine];
            });
            
            showCurrentData();
            updateStatus('تم إدخال الأرقام التجريبية: HB1=123, BCG=456, VPO=789, Penta=321, RR=654', 'pass');
        }
        
        async function saveToDatabase() {
            updateStatus('جاري حفظ البيانات في قاعدة البيانات...', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'save_monthly_planning',
                        user_id: currentUser.id,
                        center_id: currentUser.center_id,
                        planning: monthlyPlanning
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة الحفظ:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    updateStatus(`✅ تم حفظ البيانات في قاعدة البيانات: ${data.saved_months?.join(', ')}`, 'pass');
                    
                    // حفظ في localStorage أيضاً
                    localStorage.setItem(`monthlyPlanning_${currentUser.id}`, JSON.stringify(monthlyPlanning));
                    
                } else {
                    updateStatus('❌ فشل حفظ البيانات: ' + data.message, 'fail');
                }
            } catch (error) {
                updateStatus('❌ خطأ في الحفظ: ' + error.message, 'fail');
            }
        }
        
        async function loadFromDatabase() {
            updateStatus('جاري تحميل البيانات من قاعدة البيانات...', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load_monthly_planning',
                        user_id: currentUser.id
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة التحميل:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success && data.planning) {
                    monthlyPlanning = data.planning;
                    
                    // تحديث أسماء الأشهر
                    document.getElementById('month1Name').value = monthlyPlanning.month1?.name || '';
                    document.getElementById('month2Name').value = monthlyPlanning.month2?.name || '';
                    
                    // تحديث العرض
                    updateDisplay();
                    
                    // تحديث قيم الحقول
                    setTimeout(() => {
                        ['month1', 'month2'].forEach(month => {
                            if (monthlyPlanning[month] && monthlyPlanning[month].vaccines) {
                                Object.keys(monthlyPlanning[month].vaccines).forEach(vaccine => {
                                    const input = document.getElementById(`${month}_${vaccine}`);
                                    if (input) {
                                        input.value = monthlyPlanning[month].vaccines[vaccine] || 0;
                                    }
                                });
                            }
                        });
                    }, 100);
                    
                    showCurrentData();
                    updateStatus(`✅ تم تحميل البيانات من قاعدة البيانات: ${data.loaded_months?.join(', ')}`, 'pass');
                    
                } else {
                    updateStatus('⚠️ لا توجد بيانات في قاعدة البيانات', 'warning');
                }
            } catch (error) {
                updateStatus('❌ خطأ في التحميل: ' + error.message, 'fail');
            }
        }
        
        function clearAllData() {
            if (!confirm('هل أنت متأكد من مسح جميع البيانات؟')) return;
            
            monthlyPlanning = {};
            localStorage.removeItem(`monthlyPlanning_${currentUser.id}`);
            
            document.getElementById('month1Name').value = '';
            document.getElementById('month2Name').value = '';
            
            ['month1Grid', 'month2Grid'].forEach(gridId => {
                document.getElementById(gridId).innerHTML = '';
            });
            
            initializeData();
            updateStatus('تم مسح جميع البيانات', 'warning');
        }
        
        function compareBeforeAfter() {
            const comparison = document.getElementById('comparisonResults');
            
            let html = '<div class="comparison">';
            html += '<div class="before"><h5>قبل:</h5><pre>' + JSON.stringify(beforeData, null, 2) + '</pre></div>';
            html += '<div class="after"><h5>بعد:</h5><pre>' + JSON.stringify(monthlyPlanning, null, 2) + '</pre></div>';
            html += '</div>';
            
            comparison.innerHTML = html;
            
            updateStatus('تم عرض مقارنة البيانات قبل وبعد', 'info');
        }
        
        function simulateNewBrowser() {
            updateStatus('محاكاة متصفح جديد - مسح البيانات المحلية وإعادة التحميل...', 'info');
            
            // مسح البيانات المحلية
            monthlyPlanning = {};
            
            // إعادة تهيئة
            initializeData();
            
            // محاولة تحميل من قاعدة البيانات
            setTimeout(() => {
                loadFromDatabase();
            }, 1000);
        }
        
        function showCurrentData() {
            document.getElementById('currentData').textContent = JSON.stringify(monthlyPlanning, null, 2);
            
            // عرض البيانات المحفوظة في localStorage
            const saved = localStorage.getItem(`monthlyPlanning_${currentUser.id}`);
            document.getElementById('savedData').textContent = saved ? JSON.stringify(JSON.parse(saved), null, 2) : 'لا توجد بيانات محفوظة';
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            initializeData();
            updateStatus('جاهز لاختبار حفظ وتحميل الأرقام', 'info');
        };
    </script>
</body>
</html>
