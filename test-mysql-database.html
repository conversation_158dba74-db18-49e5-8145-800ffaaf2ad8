<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌍 اختبار قاعدة البيانات MySQL</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #28a745; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .stock-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stock-item h5 { margin: 0 0 10px 0; color: #28a745; }
        .stock-input { 
            width: 80px; padding: 8px; margin: 0 10px;
            border: 2px solid #ced4da; border-radius: 4px; text-align: center;
        }
        .month-name-input { 
            width: 200px; padding: 10px; margin: 10px 0;
            border: 2px solid #ced4da; border-radius: 5px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.database { background: #28a745; }
        button.database:hover { background: #218838; }
        button.test { background: #ffc107; color: #212529; }
        button.test:hover { background: #e0a800; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .grid-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .month-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: #f8f9fa; }
        .database-info {
            background: #e7f3ff;
            border: 2px solid #007bff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🌍 اختبار قاعدة البيانات MySQL للاستخدام العالمي</h1>
    
    <div class="database-info">
        <h3>📊 معلومات قاعدة البيانات</h3>
        <p><strong>الخادم:</strong> MySQL على Hostinger</p>
        <p><strong>قاعدة البيانات:</strong> csdb</p>
        <p><strong>الجدول:</strong> vaccine_monthly_planning</p>
        <p><strong>الهدف:</strong> حفظ البيانات عالمياً بدلاً من localStorage</p>
    </div>
    
    <div class="test-section">
        <h2>🎯 اختبارات قاعدة البيانات</h2>
        <button onclick="testDatabaseConnection()" class="database">اختبار الاتصال بقاعدة البيانات</button>
        <button onclick="saveToDatabase()" class="database">حفظ في قاعدة البيانات</button>
        <button onclick="loadFromDatabase()" class="database">تحميل من قاعدة البيانات</button>
        <button onclick="clearDatabase()" class="database">مسح قاعدة البيانات</button>
        <button onclick="runFullTest()" class="test">تشغيل اختبار شامل</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار قاعدة البيانات
    </div>
    
    <div class="grid-container">
        <div>
            <div class="test-section">
                <h3>📝 التخطيط الشهري</h3>
                
                <div class="month-section">
                    <h4>الشهر الأول</h4>
                    <input type="text" class="month-name-input" id="month1Name" 
                           placeholder="اسم الشهر الأول" 
                           onchange="updateMonthName('month1')" 
                           onblur="updateMonthName('month1')">
                    <div id="month1Grid"></div>
                </div>
                
                <div class="month-section">
                    <h4>الشهر الثاني</h4>
                    <input type="text" class="month-name-input" id="month2Name" 
                           placeholder="اسم الشهر الثاني"
                           onchange="updateMonthName('month2')" 
                           onblur="updateMonthName('month2')">
                    <div id="month2Grid"></div>
                </div>
            </div>
        </div>
        
        <div>
            <div class="test-section">
                <h3>📊 حالة البيانات</h3>
                <div id="dataStatus"></div>
                <h4>البيانات الحالية:</h4>
                <pre id="dataDisplay">لا توجد بيانات</pre>
            </div>
        </div>
    </div>

    <script>
        let monthlyPlanning = {};
        let currentUser = { id: 'test_user_mysql', center_id: '1' };
        
        const vaccineDefinitions = {
            'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B' },
            'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)' },
            'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)' },
            'Penta': { name: 'الخماسي', frenchName: 'Pentavalent' },
            'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)' }
        };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function initializeData() {
            const defaultVaccines = {};
            Object.keys(vaccineDefinitions).forEach(key => {
                defaultVaccines[key] = 0;
            });
            
            monthlyPlanning = {
                month1: { name: '', vaccines: {...defaultVaccines} },
                month2: { name: '', vaccines: {...defaultVaccines} }
            };
            
            updateDisplay();
            showCurrentData();
            updateStatus('تم تهيئة البيانات', 'info');
        }
        
        function updateDisplay() {
            ['month1', 'month2'].forEach(month => {
                const grid = document.getElementById(`${month}Grid`);
                grid.innerHTML = '';
                
                Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                    const vaccine = vaccineDefinitions[vaccineKey];
                    const currentValue = monthlyPlanning[month].vaccines[vaccineKey] || 0;
                    
                    const vaccineItem = document.createElement('div');
                    vaccineItem.className = 'stock-item';
                    vaccineItem.innerHTML = `
                        <h5>${vaccine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; margin-bottom: 8px;">
                            ${vaccine.frenchName}
                        </div>
                        <div>
                            <input type="number" class="stock-input" id="${month}_${vaccineKey}"
                                   value="${currentValue}" min="0" max="1000"
                                   onchange="updateVaccine('${month}', '${vaccineKey}')"
                                   onblur="updateVaccine('${month}', '${vaccineKey}')">
                            <span>قارورة</span>
                        </div>
                    `;
                    grid.appendChild(vaccineItem);
                });
            });
        }
        
        function updateVaccine(month, vaccineKey) {
            const input = document.getElementById(`${month}_${vaccineKey}`);
            const value = parseInt(input.value) || 0;
            
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].vaccines[vaccineKey] = value;
            
            showCurrentData();
            updateStatus(`تم تحديث ${vaccineKey} في ${month}: ${value}`, 'pass');
        }
        
        function updateMonthName(month) {
            const input = document.getElementById(`${month}Name`);
            const monthName = input.value.trim();
            
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].name = monthName;
            
            showCurrentData();
            updateStatus(`تم تحديث اسم ${month}: "${monthName}"`, 'pass');
        }
        
        async function testDatabaseConnection() {
            updateStatus('جاري اختبار الاتصال بقاعدة البيانات...', 'info');

            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load_monthly_planning',
                        user_id: 'test_connection'
                    })
                });

                console.log('حالة الاستجابة:', response.status);
                console.log('نوع المحتوى:', response.headers.get('content-type'));

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const responseText = await response.text();
                console.log('نص الاستجابة:', responseText);

                if (!responseText.trim()) {
                    throw new Error('الاستجابة فارغة من الخادم');
                }

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (jsonError) {
                    throw new Error(`خطأ في تحليل JSON: ${jsonError.message}. الاستجابة: ${responseText.substring(0, 100)}`);
                }

                if (data.success) {
                    updateStatus('✅ الاتصال بقاعدة البيانات ناجح', 'pass');
                } else {
                    updateStatus('❌ فشل الاتصال بقاعدة البيانات: ' + data.message, 'fail');
                }
            } catch (error) {
                console.error('خطأ مفصل:', error);
                updateStatus('❌ خطأ في الاتصال: ' + error.message, 'fail');
            }
        }
        
        async function saveToDatabase() {
            updateStatus('جاري حفظ البيانات في قاعدة البيانات...', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'save_monthly_planning',
                        user_id: currentUser.id,
                        center_id: currentUser.center_id,
                        planning: monthlyPlanning
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus(`✅ تم حفظ البيانات في قاعدة البيانات: ${data.saved_months?.join(', ')}`, 'pass');
                } else {
                    updateStatus('❌ فشل حفظ البيانات: ' + data.message, 'fail');
                }
            } catch (error) {
                updateStatus('❌ خطأ في الحفظ: ' + error.message, 'fail');
            }
        }
        
        async function loadFromDatabase() {
            updateStatus('جاري تحميل البيانات من قاعدة البيانات...', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load_monthly_planning',
                        user_id: currentUser.id
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.planning) {
                    monthlyPlanning = data.planning;
                    
                    // تحديث أسماء الأشهر
                    document.getElementById('month1Name').value = monthlyPlanning.month1?.name || '';
                    document.getElementById('month2Name').value = monthlyPlanning.month2?.name || '';
                    
                    // تحديث العرض
                    updateDisplay();
                    showCurrentData();
                    
                    updateStatus(`✅ تم تحميل البيانات من قاعدة البيانات: ${data.loaded_months?.join(', ')}`, 'pass');
                } else {
                    updateStatus('⚠️ لا توجد بيانات في قاعدة البيانات أو تم تحميل البيانات الافتراضية', 'warning');
                }
            } catch (error) {
                updateStatus('❌ خطأ في التحميل: ' + error.message, 'fail');
            }
        }
        
        async function clearDatabase() {
            if (!confirm('هل أنت متأكد من مسح البيانات من قاعدة البيانات؟')) {
                return;
            }
            
            updateStatus('جاري مسح البيانات من قاعدة البيانات...', 'warning');
            
            // مسح البيانات المحلية أولاً
            monthlyPlanning = {};
            document.getElementById('month1Name').value = '';
            document.getElementById('month2Name').value = '';
            
            ['month1Grid', 'month2Grid'].forEach(gridId => {
                document.getElementById(gridId).innerHTML = '';
            });
            
            // ثم حفظ البيانات الفارغة في قاعدة البيانات
            await saveToDatabase();
            
            updateStatus('تم مسح البيانات من قاعدة البيانات', 'warning');
            showCurrentData();
        }
        
        async function runFullTest() {
            updateStatus('بدء الاختبار الشامل...', 'info');
            
            // 1. اختبار الاتصال
            await testDatabaseConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 2. إضافة بيانات تجريبية
            document.getElementById('month1Name').value = 'يناير 2025';
            document.getElementById('month2Name').value = 'فبراير 2025';
            updateMonthName('month1');
            updateMonthName('month2');
            
            document.getElementById('month1_HB1').value = 50;
            document.getElementById('month1_BCG').value = 30;
            document.getElementById('month2_Penta').value = 40;
            updateVaccine('month1', 'HB1');
            updateVaccine('month1', 'BCG');
            updateVaccine('month2', 'Penta');
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 3. حفظ في قاعدة البيانات
            await saveToDatabase();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 4. مسح البيانات المحلية
            monthlyPlanning = {};
            updateDisplay();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 5. تحميل من قاعدة البيانات
            await loadFromDatabase();
            
            updateStatus('✅ اكتمل الاختبار الشامل بنجاح', 'pass');
        }
        
        function showCurrentData() {
            const display = document.getElementById('dataDisplay');
            display.textContent = JSON.stringify(monthlyPlanning, null, 2);
            
            // عرض إحصائيات
            const statusDiv = document.getElementById('dataStatus');
            const totalVaccines = Object.values(monthlyPlanning).reduce((total, month) => {
                return total + Object.values(month.vaccines || {}).reduce((sum, qty) => sum + qty, 0);
            }, 0);
            
            const monthsWithNames = Object.values(monthlyPlanning).filter(month => month.name && month.name.trim()).length;
            
            statusDiv.innerHTML = `
                <div style="background: #e9ecef; padding: 10px; border-radius: 5px; margin: 10px 0;">
                    <strong>📊 الإحصائيات:</strong><br>
                    🗓️ أشهر بأسماء: ${monthsWithNames}<br>
                    💉 إجمالي القوارير: ${totalVaccines}<br>
                    🌍 مصدر البيانات: قاعدة البيانات MySQL<br>
                    👤 معرف المستخدم: ${currentUser.id}
                </div>
            `;
        }
        
        // تهيئة تلقائية عند تحميل الصفحة
        window.onload = function() {
            updateStatus('تم تحميل صفحة اختبار قاعدة البيانات', 'info');
            initializeData();
            
            setTimeout(() => {
                testDatabaseConnection();
            }, 1000);
        };
    </script>
</body>
</html>
