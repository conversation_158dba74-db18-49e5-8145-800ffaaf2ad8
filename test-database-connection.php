<?php
/**
 * Test Database Connection with Exact Credentials
 * اختبار اتصال قاعدة البيانات بالبيانات الصحيحة
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'credentials_used' => [
        'host' => 'localhost',
        'database' => 'csdb',
        'username' => 'csdbuser',
        'password' => 'kTVc4ERbgOLERL9B63R5'
    ],
    'tests' => []
];

// Test 1: Direct PDO connection
try {
    $dsn = "mysql:host=localhost;dbname=csdb;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, 'csdbuser', 'kTVc4ERbgOLERL9B63R5', $options);
    
    $response['tests'][] = [
        'test' => 'Direct PDO Connection',
        'status' => 'SUCCESS',
        'message' => 'Connected successfully'
    ];
    
    // Test 2: Check database exists
    $stmt = $pdo->query("SELECT DATABASE() as current_db");
    $current_db = $stmt->fetch()['current_db'];
    
    $response['tests'][] = [
        'test' => 'Database Selection',
        'status' => 'SUCCESS',
        'current_database' => $current_db
    ];
    
    // Test 3: List all tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $response['tests'][] = [
        'test' => 'Tables Check',
        'status' => 'SUCCESS',
        'table_count' => count($tables),
        'tables' => $tables
    ];
    
    // Test 4: Check specific tables for APIs
    $required_tables = [
        'users' => 'User management',
        'children' => 'Children registry',
        'medicines' => 'Medicine management',
        'vaccines' => 'Vaccine management',
        'contraceptives' => 'Family planning',
        'centers' => 'Healthcare centers'
    ];
    
    $table_status = [];
    foreach ($required_tables as $table => $description) {
        if (in_array($table, $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            $table_status[$table] = [
                'exists' => true,
                'record_count' => $count,
                'description' => $description
            ];
        } else {
            $table_status[$table] = [
                'exists' => false,
                'record_count' => 0,
                'description' => $description
            ];
        }
    }
    
    $response['tests'][] = [
        'test' => 'Required Tables Check',
        'status' => 'SUCCESS',
        'table_status' => $table_status
    ];
    
    // Test 5: Test database-live.php config
    require_once 'config/database-live.php';
    $config_pdo = getDatabaseConnection();
    
    $stmt = $config_pdo->query("SELECT 'Config file works' as test");
    $config_test = $stmt->fetch()['test'];
    
    $response['tests'][] = [
        'test' => 'Config File Test',
        'status' => 'SUCCESS',
        'message' => $config_test
    ];
    
    // Test 6: Test main Database class
    require_once 'config/database.php';
    $db = new Database();
    $class_pdo = $db->getConnection();
    
    $stmt = $class_pdo->query("SELECT 'Database class works' as test");
    $class_test = $stmt->fetch()['test'];
    
    $response['tests'][] = [
        'test' => 'Database Class Test',
        'status' => 'SUCCESS',
        'message' => $class_test
    ];
    
    // Summary
    $response['success'] = true;
    $response['message'] = 'All database tests passed successfully';
    $response['summary'] = [
        'connection' => 'SUCCESS',
        'database' => $current_db,
        'total_tables' => count($tables),
        'config_files' => 'WORKING',
        'ready_for_apis' => true
    ];
    
} catch (Exception $e) {
    $response['tests'][] = [
        'test' => 'Database Connection',
        'status' => 'FAILED',
        'error' => $e->getMessage()
    ];
    
    $response['success'] = false;
    $response['message'] = 'Database connection failed: ' . $e->getMessage();
    
    // Additional troubleshooting info
    $response['troubleshooting'] = [
        'check_credentials' => 'Verify database name, username, and password',
        'check_mysql_service' => 'Ensure MySQL service is running',
        'check_user_permissions' => 'Verify csdbuser has access to csdb database',
        'check_host' => 'Try 127.0.0.1 instead of localhost if needed'
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
