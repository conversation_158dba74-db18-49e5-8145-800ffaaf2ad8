<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ التحقق الشامل من تحديث stock-item</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .verification-section { 
            border: 2px solid #28a745; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .stock-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .stock-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .stock-item h4 { margin: 0 0 10px 0; color: #28a745; }
        .stock-display { font-size: 1.3rem; color: #28a745; font-weight: bold; margin: 10px 0; }
        .stock-input { 
            width: 80px; padding: 8px; margin: 0 10px;
            border: 2px solid #ced4da; border-radius: 4px; text-align: center;
        }
        .month-name-input { 
            width: 200px; padding: 10px; margin: 10px 0;
            border: 2px solid #ced4da; border-radius: 5px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .test-result { 
            padding: 10px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .grid-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .month-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: #f8f9fa; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>✅ التحقق الشامل من تحديث stock-item</h1>
    
    <div class="verification-section">
        <h2>🎯 اختبارات التحقق</h2>
        <div id="testResults"></div>
        <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        <button onclick="resetTest()">إعادة تعيين</button>
    </div>
    
    <div class="grid-container">
        <div>
            <div class="verification-section">
                <h3>📝 التخطيط الشهري</h3>
                
                <div class="month-section">
                    <h4>الشهر الأول</h4>
                    <input type="text" class="month-name-input" id="month1Name" 
                           placeholder="اسم الشهر الأول" 
                           onchange="updateMonthName('month1')" 
                           onblur="updateMonthName('month1')">
                    <div id="month1Grid"></div>
                </div>
                
                <div class="month-section">
                    <h4>الشهر الثاني</h4>
                    <input type="text" class="month-name-input" id="month2Name" 
                           placeholder="اسم الشهر الثاني"
                           onchange="updateMonthName('month2')" 
                           onblur="updateMonthName('month2')">
                    <div id="month2Grid"></div>
                </div>
                
                <div class="month-section">
                    <h4>الشهر الثالث</h4>
                    <input type="text" class="month-name-input" id="month3Name" 
                           placeholder="اسم الشهر الثالث"
                           onchange="updateMonthName('month3')" 
                           onblur="updateMonthName('month3')">
                    <div id="month3Grid"></div>
                </div>
            </div>
        </div>
        
        <div>
            <div class="verification-section">
                <h3>📊 المخزون الإجمالي</h3>
                <div id="stockGrid"></div>
            </div>
        </div>
    </div>
    
    <div class="verification-section">
        <h3>💾 البيانات المحفوظة</h3>
        <pre id="dataDisplay">لا توجد بيانات</pre>
    </div>

    <script>
        let monthlyPlanning = {};
        let vaccineStock = {};
        let currentUser = { id: 'test_user' };
        
        const vaccineDefinitions = {
            'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B', description: 'جرعة واحدة عند الولادة' },
            'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)', description: 'جرعة واحدة عند الولادة' },
            'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)', description: 'جميع الجرعات' },
            'Penta': { name: 'الخماسي', frenchName: 'Pentavalent', description: 'جميع الجرعات: 2، 4، 6 أشهر' },
            'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)', description: 'جميع الجرعات: 9، 18 شهر' }
        };
        
        function initializeData() {
            const defaultVaccines = {};
            Object.keys(vaccineDefinitions).forEach(key => {
                defaultVaccines[key] = 0;
            });
            
            monthlyPlanning = {
                month1: { name: '', vaccines: {...defaultVaccines} },
                month2: { name: '', vaccines: {...defaultVaccines} },
                month3: { name: '', vaccines: {...defaultVaccines} }
            };
            
            calculateTotalStock();
            updateDisplay();
            showCurrentData();
        }
        
        function updateDisplay() {
            displayMonthlyGrids();
            displayStockGrid();
        }
        
        function displayMonthlyGrids() {
            ['month1', 'month2', 'month3'].forEach(month => {
                const grid = document.getElementById(`${month}Grid`);
                grid.innerHTML = '';
                
                Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                    const vaccine = vaccineDefinitions[vaccineKey];
                    const currentValue = monthlyPlanning[month].vaccines[vaccineKey] || 0;
                    
                    const vaccineItem = document.createElement('div');
                    vaccineItem.className = 'stock-item';
                    vaccineItem.style.marginBottom = '10px';
                    vaccineItem.innerHTML = `
                        <h5 style="margin-bottom: 5px; color: #28a745;">${vaccine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; font-style: italic; margin-bottom: 8px;">
                            ${vaccine.frenchName}
                        </div>
                        <div class="stock-controls">
                            <input type="number" class="stock-input" id="${month}_${vaccineKey}"
                                   value="${currentValue}" min="0" max="1000"
                                   onchange="updateMonthlyVaccine('${month}', '${vaccineKey}')"
                                   onblur="updateMonthlyVaccine('${month}', '${vaccineKey}')">
                            <span style="margin-right: 5px; font-size: 0.9em;">قارورة</span>
                        </div>
                    `;
                    grid.appendChild(vaccineItem);
                });
            });
        }
        
        function displayStockGrid() {
            const stockGrid = document.getElementById('stockGrid');
            stockGrid.innerHTML = '';
            
            Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                const vaccine = vaccineDefinitions[vaccineKey];
                const totalStock = vaccineStock[vaccineKey] || 0;
                const month1Qty = monthlyPlanning.month1.vaccines[vaccineKey] || 0;
                const month2Qty = monthlyPlanning.month2.vaccines[vaccineKey] || 0;
                const month3Qty = monthlyPlanning.month3.vaccines[vaccineKey] || 0;
                const status = getStockStatus(totalStock);
                
                const stockItem = document.createElement('div');
                stockItem.className = 'stock-item';
                stockItem.innerHTML = `
                    <h4>${vaccine.name}</h4>
                    <div style="font-size: 0.85em; color: #6c757d; font-style: italic; margin-bottom: 5px;">
                        ${vaccine.frenchName}
                    </div>
                    <div style="font-size: 0.8em; color: #495057; margin-bottom: 10px; line-height: 1.3;">
                        ${vaccine.description}
                    </div>
                    <div class="stock-display">
                        المجموع: ${totalStock} قارورة
                    </div>
                    <div style="font-size: 0.9em; color: #6c757d; margin: 8px 0;">
                        <div>📅 ${monthlyPlanning.month1.name || 'الشهر الأول'}: ${month1Qty}</div>
                        <div>📅 ${monthlyPlanning.month2.name || 'الشهر الثاني'}: ${month2Qty}</div>
                        <div>📅 ${monthlyPlanning.month3.name || 'الشهر الثالث'}: ${month3Qty}</div>
                    </div>
                    <span class="stock-status ${status.class}">${status.text}</span>
                `;
                stockGrid.appendChild(stockItem);
            });
        }
        
        function getStockStatus(stock) {
            if (stock > 1) {
                return { class: 'available', text: 'متوفر' };
            } else {
                return { class: 'unavailable', text: 'غير متوفر' };
            }
        }
        
        function calculateTotalStock() {
            vaccineStock = {};
            
            Object.keys(monthlyPlanning.month1.vaccines).forEach(vaccine => {
                const month1Qty = monthlyPlanning.month1.vaccines[vaccine] || 0;
                const month2Qty = monthlyPlanning.month2.vaccines[vaccine] || 0;
                const month3Qty = monthlyPlanning.month3.vaccines[vaccine] || 0;
                
                vaccineStock[vaccine] = month1Qty + month2Qty + month3Qty;
            });
        }
        
        async function updateMonthlyVaccine(month, vaccineKey) {
            const input = document.getElementById(`${month}_${vaccineKey}`);
            const value = parseInt(input.value) || 0;
            
            monthlyPlanning[month].vaccines[vaccineKey] = value;
            
            // حفظ في localStorage
            localStorage.setItem(`monthlyPlanning_${currentUser.id}`, JSON.stringify(monthlyPlanning));
            
            // إعادة حساب المجموع الإجمالي
            calculateTotalStock();
            displayStockGrid();
            showCurrentData();
            
            console.log(`تم تحديث ${vaccineKey} في ${month}: ${value}`);
        }
        
        async function updateMonthName(month) {
            const input = document.getElementById(`${month}Name`);
            const monthName = input.value.trim();
            
            monthlyPlanning[month].name = monthName;
            
            // حفظ في localStorage
            localStorage.setItem(`monthlyPlanning_${currentUser.id}`, JSON.stringify(monthlyPlanning));
            
            // تحديث عرض المخزون الإجمالي
            displayStockGrid();
            showCurrentData();
            
            console.log(`تم تحديث اسم ${month}: ${monthName}`);
        }
        
        function runAllTests() {
            const tests = [
                testMonthNameUpdate,
                testVaccineQuantityUpdate,
                testTotalStockCalculation,
                testDataPersistence,
                testStockItemDisplay
            ];
            
            const results = document.getElementById('testResults');
            results.innerHTML = '';
            
            tests.forEach((test, index) => {
                const result = test();
                const div = document.createElement('div');
                div.className = `test-result ${result.pass ? 'pass' : 'fail'}`;
                div.innerHTML = `${index + 1}. ${result.name}: ${result.pass ? '✅ نجح' : '❌ فشل'} - ${result.message}`;
                results.appendChild(div);
            });
        }
        
        function testMonthNameUpdate() {
            document.getElementById('month1Name').value = 'يناير 2025';
            updateMonthName('month1');
            
            const saved = JSON.parse(localStorage.getItem(`monthlyPlanning_${currentUser.id}`));
            const pass = saved && saved.month1.name === 'يناير 2025';
            
            return {
                name: 'تحديث اسم الشهر',
                pass: pass,
                message: pass ? 'تم حفظ اسم الشهر بنجاح' : 'فشل في حفظ اسم الشهر'
            };
        }
        
        function testVaccineQuantityUpdate() {
            const input = document.getElementById('month1_HB1');
            if (!input) {
                return {
                    name: 'تحديث كمية اللقاح',
                    pass: false,
                    message: 'لم يتم العثور على حقل month1_HB1'
                };
            }

            input.value = 50;
            updateMonthlyVaccine('month1', 'HB1');

            // انتظار قصير للتأكد من التحديث
            setTimeout(() => {
                calculateTotalStock();
            }, 100);

            const dataUpdated = monthlyPlanning.month1.vaccines.HB1 === 50;
            const stockUpdated = vaccineStock.HB1 >= 50; // قد يكون أكبر إذا كانت هناك كميات في أشهر أخرى
            const pass = dataUpdated && stockUpdated;

            return {
                name: 'تحديث كمية اللقاح',
                pass: pass,
                message: pass ?
                    `تم تحديث الكمية: البيانات=${monthlyPlanning.month1.vaccines.HB1}, المخزون=${vaccineStock.HB1}` :
                    `فشل في التحديث: البيانات=${monthlyPlanning.month1.vaccines.HB1}, المخزون=${vaccineStock.HB1}`
            };
        }
        
        function testTotalStockCalculation() {
            document.getElementById('month1_BCG').value = 30;
            document.getElementById('month2_BCG').value = 20;
            document.getElementById('month3_BCG').value = 25;
            
            updateMonthlyVaccine('month1', 'BCG');
            updateMonthlyVaccine('month2', 'BCG');
            updateMonthlyVaccine('month3', 'BCG');
            
            const expectedTotal = 30 + 20 + 25;
            const pass = vaccineStock.BCG === expectedTotal;
            
            return {
                name: 'حساب المجموع الإجمالي',
                pass: pass,
                message: pass ? `المجموع صحيح: ${expectedTotal}` : `المجموع خاطئ: ${vaccineStock.BCG} بدلاً من ${expectedTotal}`
            };
        }
        
        function testDataPersistence() {
            const testData = JSON.stringify(monthlyPlanning);
            localStorage.setItem(`monthlyPlanning_${currentUser.id}`, testData);
            
            const retrieved = localStorage.getItem(`monthlyPlanning_${currentUser.id}`);
            const pass = retrieved === testData;
            
            return {
                name: 'حفظ البيانات',
                pass: pass,
                message: pass ? 'تم حفظ البيانات في localStorage' : 'فشل في حفظ البيانات'
            };
        }
        
        function testStockItemDisplay() {
            const stockItems = document.querySelectorAll('#stockGrid .stock-item');
            const monthlyItems = document.querySelectorAll('[id$="Grid"] .stock-item');
            
            const pass = stockItems.length > 0 && monthlyItems.length > 0;
            
            return {
                name: 'عرض عناصر stock-item',
                pass: pass,
                message: pass ? `تم عرض ${stockItems.length} عنصر مخزون و ${monthlyItems.length} عنصر شهري` : 'فشل في عرض العناصر'
            };
        }
        
        function resetTest() {
            localStorage.removeItem(`monthlyPlanning_${currentUser.id}`);
            initializeData();
            document.getElementById('testResults').innerHTML = '';
        }
        
        function showCurrentData() {
            const display = document.getElementById('dataDisplay');
            display.textContent = JSON.stringify({
                monthlyPlanning: monthlyPlanning,
                vaccineStock: vaccineStock
            }, null, 2);
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            initializeData();
        };
    </script>
</body>
</html>
