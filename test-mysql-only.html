<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗄️ اختبار MySQL فقط</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .success-section { border-color: #28a745; }
        .warning-section { border-color: #ffc107; }
        .error-section { border-color: #dc3545; }
        .stock-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stock-item h5 { margin: 0 0 10px 0; color: #28a745; }
        .stock-input { 
            width: 80px; padding: 8px; margin: 0 10px;
            border: 2px solid #ced4da; border-radius: 4px; text-align: center;
            font-size: 16px; font-weight: bold;
        }
        .stock-input:focus { border-color: #007bff; outline: none; }
        .month-name-input { 
            width: 200px; padding: 10px; margin: 10px 0;
            border: 2px solid #ced4da; border-radius: 5px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.save { background: #28a745; }
        button.save:hover { background: #218838; }
        button.load { background: #17a2b8; }
        button.load:hover { background: #138496; }
        button.clear { background: #dc3545; }
        button.clear:hover { background: #c82333; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { 
            background: #f8f9fa; padding: 15px; border-radius: 5px; 
            overflow-x: auto; font-size: 12px; white-space: pre-wrap;
        }
        .grid-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .month-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: #f8f9fa; }
        .test-steps {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🗄️ اختبار النظام مع MySQL فقط (بدون localStorage)</h1>
    
    <div class="test-section success-section">
        <h2>✅ المزايا الجديدة</h2>
        <ul>
            <li>🌍 <strong>عمل عالمي:</strong> البيانات محفوظة في MySQL ويمكن الوصول إليها من أي مكان</li>
            <li>🔄 <strong>مزامنة تلقائية:</strong> البيانات متزامنة بين جميع الأجهزة</li>
            <li>💾 <strong>حفظ دائم:</strong> لا فقدان للبيانات حتى لو تم مسح المتصفح</li>
            <li>👥 <strong>متعدد المستخدمين:</strong> كل مستخدم له بياناته الخاصة</li>
        </ul>
    </div>
    
    <div class="test-section warning-section">
        <h2>⚠️ ما تم إزالته</h2>
        <ul>
            <li>❌ <strong>localStorage:</strong> لا مزيد من التخزين المحلي</li>
            <li>❌ <strong>النسخ الاحتياطية المحلية:</strong> MySQL هو المصدر الوحيد</li>
            <li>❌ <strong>التعارضات:</strong> لا مزيد من تعارض البيانات المحلية مع الخادم</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 خطوات الاختبار</h2>
        <div class="test-steps">
            <h3>الخطوة 1: إدخال البيانات</h3>
            <p>أدخل أسماء الأشهر وكميات اللقاحات في الحقول أدناه</p>
        </div>
        <div class="test-steps">
            <h3>الخطوة 2: حفظ في MySQL</h3>
            <p>اضغط "💾 حفظ في MySQL" لحفظ البيانات في قاعدة البيانات</p>
        </div>
        <div class="test-steps">
            <h3>الخطوة 3: محاكاة متصفح جديد</h3>
            <p>اضغط "🌐 محاكاة متصفح جديد" لمسح البيانات المحلية وإعادة التحميل</p>
        </div>
        <div class="test-steps">
            <h3>الخطوة 4: التحقق من النتائج</h3>
            <p>تأكد من ظهور نفس البيانات المحفوظة</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 أدوات التحكم</h2>
        <button onclick="setTestData()">📝 إدخال بيانات تجريبية</button>
        <button onclick="saveToMySQL()" class="save">💾 حفظ في MySQL</button>
        <button onclick="loadFromMySQL()" class="load">📂 تحميل من MySQL</button>
        <button onclick="simulateNewBrowser()">🌐 محاكاة متصفح جديد</button>
        <button onclick="clearMySQL()" class="clear">🗑️ مسح MySQL</button>
        <button onclick="checkLocalStorage()">🔍 فحص localStorage</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار MySQL فقط
    </div>
    
    <div class="grid-container">
        <div>
            <div class="test-section">
                <h3>📝 إدخال البيانات</h3>
                
                <div class="month-section">
                    <h4>الشهر الأول</h4>
                    <input type="text" class="month-name-input" id="month1Name" 
                           placeholder="اسم الشهر الأول" 
                           onchange="updateMonthName('month1')" 
                           onblur="updateMonthName('month1')">
                    <div id="month1Grid"></div>
                </div>
                
                <div class="month-section">
                    <h4>الشهر الثاني</h4>
                    <input type="text" class="month-name-input" id="month2Name" 
                           placeholder="اسم الشهر الثاني"
                           onchange="updateMonthName('month2')" 
                           onblur="updateMonthName('month2')">
                    <div id="month2Grid"></div>
                </div>
            </div>
        </div>
        
        <div>
            <div class="test-section">
                <h3>📊 حالة البيانات</h3>
                <div id="dataStatus"></div>
                
                <h4>📝 البيانات الحالية:</h4>
                <pre id="currentData">لا توجد بيانات</pre>
                
                <h4>🔍 فحص localStorage:</h4>
                <pre id="localStorageCheck">لم يتم الفحص بعد</pre>
            </div>
        </div>
    </div>

    <script>
        let monthlyPlanning = {};
        let currentUser = { id: 'mysql_only_user', center_id: '1' };
        
        const vaccineDefinitions = {
            'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B' },
            'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)' },
            'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)' },
            'Penta': { name: 'الخماسي', frenchName: 'Pentavalent' },
            'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)' }
        };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function initializeData() {
            const defaultVaccines = {};
            Object.keys(vaccineDefinitions).forEach(key => {
                defaultVaccines[key] = 0;
            });
            
            monthlyPlanning = {
                month1: { name: '', vaccines: {...defaultVaccines} },
                month2: { name: '', vaccines: {...defaultVaccines} }
            };
            
            updateDisplay();
            showCurrentData();
        }
        
        function updateDisplay() {
            ['month1', 'month2'].forEach(month => {
                const grid = document.getElementById(`${month}Grid`);
                grid.innerHTML = '';
                
                Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                    const vaccine = vaccineDefinitions[vaccineKey];
                    const currentValue = monthlyPlanning[month].vaccines[vaccineKey] || 0;
                    
                    const vaccineItem = document.createElement('div');
                    vaccineItem.className = 'stock-item';
                    vaccineItem.innerHTML = `
                        <h5>${vaccine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; margin-bottom: 8px;">
                            ${vaccine.frenchName}
                        </div>
                        <div>
                            <input type="number" class="stock-input" id="${month}_${vaccineKey}"
                                   value="${currentValue}" min="0" max="1000"
                                   onchange="updateVaccine('${month}', '${vaccineKey}')"
                                   onblur="updateVaccine('${month}', '${vaccineKey}')">
                            <span>قارورة</span>
                        </div>
                    `;
                    grid.appendChild(vaccineItem);
                });
            });
        }
        
        function updateVaccine(month, vaccineKey) {
            const input = document.getElementById(`${month}_${vaccineKey}`);
            const value = parseInt(input.value) || 0;
            
            monthlyPlanning[month].vaccines[vaccineKey] = value;
            showCurrentData();
            updateStatus(`تم تحديث ${vaccineKey} في ${month}: ${value} (محلياً فقط)`, 'info');
        }
        
        function updateMonthName(month) {
            const input = document.getElementById(`${month}Name`);
            const monthName = input.value.trim();
            
            monthlyPlanning[month].name = monthName;
            showCurrentData();
            updateStatus(`تم تحديث اسم ${month}: "${monthName}" (محلياً فقط)`, 'info');
        }
        
        function setTestData() {
            const testData = {
                month1: { name: 'يناير 2025', vaccines: { HB1: 100, BCG: 200, VPO: 300 } },
                month2: { name: 'فبراير 2025', vaccines: { Penta: 400, RR: 500 } }
            };
            
            monthlyPlanning = testData;
            
            document.getElementById('month1Name').value = testData.month1.name;
            document.getElementById('month2Name').value = testData.month2.name;
            
            updateDisplay();
            showCurrentData();
            updateStatus('تم إدخال البيانات التجريبية: HB1=100, BCG=200, VPO=300, Penta=400, RR=500', 'pass');
        }
        
        async function saveToMySQL() {
            updateStatus('جاري حفظ البيانات في MySQL...', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'save_monthly_planning',
                        user_id: currentUser.id,
                        center_id: currentUser.center_id,
                        planning: monthlyPlanning
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة الحفظ:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    updateStatus(`✅ تم حفظ البيانات في MySQL بنجاح: ${data.saved_months?.join(', ')}`, 'pass');
                } else {
                    updateStatus('❌ فشل حفظ البيانات في MySQL: ' + data.message, 'fail');
                }
            } catch (error) {
                updateStatus('❌ خطأ في حفظ البيانات: ' + error.message, 'fail');
            }
        }
        
        async function loadFromMySQL() {
            updateStatus('جاري تحميل البيانات من MySQL...', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'load_monthly_planning',
                        user_id: currentUser.id
                    })
                });
                
                const responseText = await response.text();
                console.log('استجابة التحميل:', responseText);
                
                const data = JSON.parse(responseText);
                
                if (data.success && data.planning) {
                    monthlyPlanning = data.planning;
                    
                    document.getElementById('month1Name').value = monthlyPlanning.month1?.name || '';
                    document.getElementById('month2Name').value = monthlyPlanning.month2?.name || '';
                    
                    updateDisplay();
                    showCurrentData();
                    updateStatus(`✅ تم تحميل البيانات من MySQL: ${data.loaded_months?.join(', ')}`, 'pass');
                } else {
                    updateStatus('⚠️ لا توجد بيانات في MySQL', 'warning');
                }
            } catch (error) {
                updateStatus('❌ خطأ في تحميل البيانات: ' + error.message, 'fail');
            }
        }
        
        function simulateNewBrowser() {
            updateStatus('محاكاة متصفح جديد - مسح البيانات المحلية...', 'warning');
            
            // مسح البيانات المحلية
            monthlyPlanning = {};
            document.getElementById('month1Name').value = '';
            document.getElementById('month2Name').value = '';
            
            ['month1Grid', 'month2Grid'].forEach(gridId => {
                document.getElementById(gridId).innerHTML = '';
            });
            
            showCurrentData();
            
            // محاولة تحميل من MySQL
            setTimeout(() => {
                loadFromMySQL();
            }, 1000);
        }
        
        async function clearMySQL() {
            if (!confirm('هل أنت متأكد من مسح البيانات من MySQL؟')) return;
            
            updateStatus('جاري مسح البيانات من MySQL...', 'warning');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'save_monthly_planning',
                        user_id: currentUser.id,
                        center_id: currentUser.center_id,
                        planning: {
                            month1: { name: '', vaccines: {} },
                            month2: { name: '', vaccines: {} }
                        }
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateStatus('✅ تم مسح البيانات من MySQL', 'pass');
                    simulateNewBrowser();
                } else {
                    updateStatus('❌ فشل في مسح البيانات من MySQL', 'fail');
                }
            } catch (error) {
                updateStatus('❌ خطأ في مسح البيانات: ' + error.message, 'fail');
            }
        }
        
        function checkLocalStorage() {
            const keys = Object.keys(localStorage);
            const relevantKeys = keys.filter(key => key.includes('monthlyPlanning') || key.includes('vaccine'));
            
            let result = `إجمالي مفاتيح localStorage: ${keys.length}\n`;
            result += `مفاتيح متعلقة بالتطبيق: ${relevantKeys.length}\n\n`;
            
            if (relevantKeys.length > 0) {
                result += 'المفاتيح الموجودة:\n';
                relevantKeys.forEach(key => {
                    result += `- ${key}\n`;
                });
                result += '\n⚠️ يجب عدم وجود بيانات في localStorage!';
            } else {
                result += '✅ لا توجد بيانات في localStorage - ممتاز!';
            }
            
            document.getElementById('localStorageCheck').textContent = result;
            updateStatus(relevantKeys.length === 0 ? 'localStorage نظيف ✅' : 'يوجد بيانات في localStorage ⚠️', 
                        relevantKeys.length === 0 ? 'pass' : 'warning');
        }
        
        function showCurrentData() {
            document.getElementById('currentData').textContent = JSON.stringify(monthlyPlanning, null, 2);
            
            const statusDiv = document.getElementById('dataStatus');
            const totalVaccines = Object.values(monthlyPlanning).reduce((total, month) => {
                return total + Object.values(month.vaccines || {}).reduce((sum, qty) => sum + qty, 0);
            }, 0);
            
            statusDiv.innerHTML = `
                <div style="background: #e9ecef; padding: 10px; border-radius: 5px;">
                    <strong>📊 الإحصائيات:</strong><br>
                    💉 إجمالي القوارير: ${totalVaccines}<br>
                    🗄️ مصدر البيانات: MySQL فقط<br>
                    👤 معرف المستخدم: ${currentUser.id}
                </div>
            `;
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            initializeData();
            checkLocalStorage();
            updateStatus('جاهز لاختبار MySQL فقط - لا localStorage', 'info');
        };
    </script>
</body>
</html>
