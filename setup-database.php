<?php
/**
 * Database Setup Script
 * سكريبت إعداد قاعدة البيانات
 */

header('Content-Type: application/json; charset=utf-8');

// Get setup parameters
$action = $_GET['action'] ?? 'check';
$root_password = $_GET['root_password'] ?? '';

$response = [
    'success' => false,
    'message' => '',
    'action' => $action,
    'timestamp' => date('Y-m-d H:i:s')
];

try {
    switch ($action) {
        case 'check':
            // Just check current status
            $response['message'] = 'Database status check';
            $response['instructions'] = [
                'To setup database, use: setup-database.php?action=setup&root_password=YOUR_ROOT_PASSWORD',
                'To create user only: setup-database.php?action=create_user&root_password=YOUR_ROOT_PASSWORD',
                'To create database only: setup-database.php?action=create_db&root_password=YOUR_ROOT_PASSWORD'
            ];
            break;
            
        case 'setup':
        case 'create_user':
        case 'create_db':
            if (empty($root_password)) {
                throw new Exception('Root password is required. Use: ?root_password=YOUR_PASSWORD');
            }
            
            // Connect as root
            $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', $root_password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
            
            $steps_completed = [];
            
            if ($action === 'setup' || $action === 'create_db') {
                // Create database
                try {
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `csdb` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    $steps_completed[] = "✅ Database 'csdb' created/verified";
                } catch (PDOException $e) {
                    $steps_completed[] = "❌ Failed to create database: " . $e->getMessage();
                }
            }
            
            if ($action === 'setup' || $action === 'create_user') {
                // Create user and grant permissions
                try {
                    // Drop user if exists (to reset permissions)
                    try {
                        $pdo->exec("DROP USER IF EXISTS 'csdbuser'@'localhost'");
                    } catch (PDOException $e) {
                        // Ignore if user doesn't exist
                    }
                    
                    // Create user
                    $pdo->exec("CREATE USER 'csdbuser'@'localhost' IDENTIFIED BY 'kTVc4ERbgOLERL9B63R5'");
                    $steps_completed[] = "✅ User 'csdbuser' created";
                    
                    // Grant permissions
                    $pdo->exec("GRANT ALL PRIVILEGES ON `csdb`.* TO 'csdbuser'@'localhost'");
                    $pdo->exec("FLUSH PRIVILEGES");
                    $steps_completed[] = "✅ Permissions granted to 'csdbuser' on 'csdb' database";
                    
                } catch (PDOException $e) {
                    $steps_completed[] = "❌ Failed to create user: " . $e->getMessage();
                }
            }
            
            // Test the new connection
            try {
                $test_pdo = new PDO("mysql:host=localhost;dbname=csdb;charset=utf8mb4", 'csdbuser', 'kTVc4ERbgOLERL9B63R5', [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                ]);
                $test_pdo->query("SELECT 1");
                $steps_completed[] = "✅ Connection test successful";
                $response['success'] = true;
            } catch (PDOException $e) {
                $steps_completed[] = "❌ Connection test failed: " . $e->getMessage();
            }
            
            $response['steps'] = $steps_completed;
            $response['message'] = $response['success'] ? 'Database setup completed successfully' : 'Database setup completed with some errors';
            break;
            
        default:
            throw new Exception('Invalid action. Use: check, setup, create_user, or create_db');
    }
    
    if ($action === 'check') {
        $response['success'] = true;
    }
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    $response['error'] = true;
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
