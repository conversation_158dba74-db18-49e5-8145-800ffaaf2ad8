<?php
/**
 * Check Users in Database
 * فحص المستخدمين في قاعدة البيانات
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

try {
    require_once 'config/database-live.php';
    $pdo = getDatabaseConnection();
    
    // Check users table structure
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Count total users
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $total_users = $stmt->fetch()['total'];
    
    // Get all users
    $stmt = $pdo->query("SELECT id, username, name, role, is_active, created_at FROM users ORDER BY created_at DESC");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Check if we need to create sample users
    $need_sample_users = ($total_users == 0);
    
    $response = [
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'table_structure' => $columns,
        'total_users' => $total_users,
        'users' => $users,
        'need_sample_users' => $need_sample_users
    ];
    
    // Create sample users if needed
    if ($need_sample_users) {
        // First create a sample center
        $pdo->exec("INSERT IGNORE INTO centers (id, name, location, phone) VALUES 
            (1, 'المركز الصحي الرئيسي', 'المدينة المركزية', '123456789')");
        
        // Create sample users
        $sample_users = [
            [
                'id' => 'admin001',
                'username' => 'admin',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'name' => 'مدير النظام',
                'center_id' => 1,
                'role' => 'admin'
            ],
            [
                'id' => 'nurse001',
                'username' => 'nurse1',
                'password' => password_hash('nurse123', PASSWORD_DEFAULT),
                'name' => 'الممرضة الأولى',
                'center_id' => 1,
                'role' => 'nurse'
            ]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO users (id, username, password, name, center_id, role) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($sample_users as $user) {
            $stmt->execute([$user['id'], $user['username'], $user['password'], $user['name'], $user['center_id'], $user['role']]);
        }
        
        $response['sample_users_created'] = true;
        $response['created_users'] = [
            'admin: admin/admin123',
            'nurse1: nurse1/nurse123'
        ];
        
        // Reload users after creation
        $stmt = $pdo->query("SELECT id, username, name, role, is_active, created_at FROM users ORDER BY created_at DESC");
        $response['users'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response['total_users'] = count($response['users']);
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
