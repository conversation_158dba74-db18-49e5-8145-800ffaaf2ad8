<?php
/**
 * إعدادات قاعدة البيانات للخادم المباشر
 * Live Server Database Configuration
 */

// تحديد إعدادات قاعدة البيانات بناءً على البيئة
$isLiveServer = isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'csmanager.online') !== false;

// Check for CloudPanel specific configuration
$cloudpanel_config_file = __DIR__ . '/cloudpanel-config.php';
if (file_exists($cloudpanel_config_file)) {
    include $cloudpanel_config_file;
    if (isset($cloudpanel_db_config)) {
        $db_configs = [$cloudpanel_db_config];
    }
} else {

if ($isLiveServer) {
    // إعدادات الخادم المباشر - CloudPanel configurations
    $db_configs = [
        // CloudPanel typical naming convention
        [
            'host' => 'localhost',
            'dbname' => 'csmanage_csdb',
            'username' => 'csmanage_csdbuser',
            'password' => 'j5aKN6lz5bsujTcWaYAd',
            'charset' => 'utf8mb4'
        ],
        // Alternative CloudPanel naming
        [
            'host' => 'localhost',
            'dbname' => 'csmanage_main',
            'username' => 'csmanage_user',
            'password' => 'j5aKN6lz5bsujTcWaYAd',
            'charset' => 'utf8mb4'
        ],
        // Simple naming
        [
            'host' => 'localhost',
            'dbname' => 'csdb',
            'username' => 'csdbuser',
            'password' => 'j5aKN6lz5bsujTcWaYAd',
            'charset' => 'utf8mb4'
        ],
        // Try with site name prefix
        [
            'host' => 'localhost',
            'dbname' => 'csmanager_csdb',
            'username' => 'csmanager_user',
            'password' => 'j5aKN6lz5bsujTcWaYAd',
            'charset' => 'utf8mb4'
        ]
    ];
} else {
    // إعدادات الخادم المحلي - جرب عدة احتمالات
    $db_configs = [
        [
            'host' => 'localhost',
            'dbname' => 'csdb',
            'username' => 'csdbuser',
            'password' => 'j5aKN6lz5bsujTcWaYAd',
            'charset' => 'utf8mb4'
        ],
        [
            'host' => '127.0.0.1',
            'dbname' => 'csdb',
            'username' => 'csdbuser',
            'password' => 'j5aKN6lz5bsujTcWaYAd',
            'charset' => 'utf8mb4'
        ],
        [
            'host' => 'localhost',
            'dbname' => 'csdb',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4'
        ],
        [
            'host' => '127.0.0.1',
            'dbname' => 'csdb',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4'
        ]
    ];
}
}

/**
 * إنشاء اتصال قاعدة البيانات مع تجريب عدة إعدادات
 */
function getDatabaseConnection() {
    global $db_configs;

    $lastError = null;
    $setupAttempted = false;

    // جرب كل إعداد حتى ينجح واحد
    foreach ($db_configs as $config) {
        try {
            $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]);

            // اختبار الاتصال
            $pdo->query("SELECT 1");

            // إذا وصلنا هنا، الاتصال نجح
            error_log("Database connection successful with: " . $config['host'] . "/" . $config['dbname']);
            return $pdo;

        } catch (PDOException $e) {
            $lastError = $e;
            error_log("Database connection failed with {$config['host']}/{$config['dbname']}: " . $e->getMessage());

            // إذا كان الخطأ متعلق بعدم وجود قاعدة البيانات أو المستخدم، جرب الإعداد التلقائي
            if (!$setupAttempted && (
                strpos($e->getMessage(), 'Access denied') !== false ||
                strpos($e->getMessage(), 'Unknown database') !== false
            )) {
                $setupAttempted = true;
                if (attemptAutoSetup()) {
                    // إعادة المحاولة مع نفس الإعداد
                    try {
                        $pdo = new PDO($dsn, $config['username'], $config['password'], [
                            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                            PDO::ATTR_EMULATE_PREPARES => false,
                            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
                        ]);
                        $pdo->query("SELECT 1");
                        error_log("Database connection successful after auto-setup");
                        return $pdo;
                    } catch (PDOException $e2) {
                        error_log("Connection still failed after auto-setup: " . $e2->getMessage());
                    }
                }
            }
            continue;
        }
    }

    // إذا فشلت جميع المحاولات
    throw new Exception("فشل في الاتصال بقاعدة البيانات مع جميع الإعدادات. آخر خطأ: " . ($lastError ? $lastError->getMessage() : 'غير معروف'));
}

/**
 * محاولة الإعداد التلقائي لقاعدة البيانات
 */
function attemptAutoSetup() {
    $setup_attempts = [
        ['username' => 'root', 'password' => ''],
        ['username' => 'root', 'password' => 'root'],
        ['username' => 'root', 'password' => 'password'],
    ];

    foreach ($setup_attempts as $attempt) {
        try {
            $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", $attempt['username'], $attempt['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            ]);

            // إنشاء قاعدة البيانات
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `csdb` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

            // إنشاء المستخدم
            try {
                $pdo->exec("CREATE USER IF NOT EXISTS 'csdbuser'@'localhost' IDENTIFIED BY 'j5aKN6lz5bsujTcWaYAd'");
            } catch (PDOException $e) {
                // المستخدم موجود، لا مشكلة
            }

            // منح الصلاحيات
            $pdo->exec("GRANT ALL PRIVILEGES ON `csdb`.* TO 'csdbuser'@'localhost'");
            $pdo->exec("FLUSH PRIVILEGES");

            error_log("Auto-setup completed successfully");
            return true;

        } catch (PDOException $e) {
            error_log("Auto-setup attempt failed with {$attempt['username']}: " . $e->getMessage());
            continue;
        }
    }

    return false;
}

/**
 * اختبار الاتصال بقاعدة البيانات
 */
function testDatabaseConnection() {
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->query("SELECT 1");
        return true;
    } catch (Exception $e) {
        error_log("Database test failed: " . $e->getMessage());
        return false;
    }
}
?>
