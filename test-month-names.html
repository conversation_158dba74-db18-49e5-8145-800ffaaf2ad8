<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقول أسماء الأشهر</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f8f9fa;
        }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .month-name-input { 
            width: 250px; 
            padding: 10px; 
            margin: 10px;
            border: 2px solid #ced4da;
            border-radius: 5px;
            font-size: 16px;
        }
        .month-name-input:focus {
            border-color: #007bff;
            outline: none;
        }
        button { 
            padding: 10px 20px; 
            margin: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار حقول أسماء الأشهر</h1>
    
    <div class="test-section">
        <h2>📝 حقول أسماء الأشهر</h2>
        
        <div>
            <label><strong>الشهر الأول:</strong></label><br>
            <input type="text" class="month-name-input" id="month1Name" 
                   placeholder="اسم الشهر الأول" value=""
                   onchange="updateMonthName('month1')" 
                   onblur="updateMonthName('month1')">
        </div>
        
        <div>
            <label><strong>الشهر الثاني:</strong></label><br>
            <input type="text" class="month-name-input" id="month2Name" 
                   placeholder="اسم الشهر الثاني" value=""
                   onchange="updateMonthName('month2')" 
                   onblur="updateMonthName('month2')">
        </div>
        
        <div>
            <label><strong>الشهر الثالث:</strong></label><br>
            <input type="text" class="month-name-input" id="month3Name" 
                   placeholder="اسم الشهر الثالث" value=""
                   onchange="updateMonthName('month3')" 
                   onblur="updateMonthName('month3')">
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 أدوات الاختبار</h2>
        <button onclick="initializeData()">تهيئة البيانات</button>
        <button onclick="saveTestData()">حفظ بيانات تجريبية</button>
        <button onclick="loadSavedData()">تحميل البيانات المحفوظة</button>
        <button onclick="clearData()">مسح البيانات</button>
        <button onclick="showCurrentData()">عرض البيانات الحالية</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز للاختبار
    </div>
    
    <div class="test-section">
        <h2>📊 البيانات الحالية</h2>
        <pre id="dataDisplay">لا توجد بيانات</pre>
    </div>

    <script>
        let monthlyPlanning = {};
        let currentUser = { id: 'test_user' };
        
        // تهيئة البيانات
        function initializeData() {
            monthlyPlanning = {
                month1: { name: '', vaccines: {} },
                month2: { name: '', vaccines: {} },
                month3: { name: '', vaccines: {} }
            };
            
            updateStatus('تم تهيئة البيانات', 'success');
            showCurrentData();
        }
        
        // دالة تحديث اسم الشهر
        async function updateMonthName(month) {
            const input = document.getElementById(`${month}Name`);
            if (!input) {
                updateStatus(`❌ لم يتم العثور على حقل ${month}Name`, 'error');
                return;
            }

            const monthName = input.value.trim();
            
            // تحديث البيانات المحلية
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].name = monthName;

            // حفظ في localStorage
            if (currentUser) {
                localStorage.setItem(`monthlyPlanning_${currentUser.id}`, JSON.stringify(monthlyPlanning));
            }

            updateStatus(`✅ تم حفظ اسم ${month}: "${monthName}"`, 'success');
            showCurrentData();
            
            console.log(`تم تحديث ${month}:`, monthName);
        }
        
        // حفظ بيانات تجريبية
        function saveTestData() {
            document.getElementById('month1Name').value = 'يناير 2025';
            document.getElementById('month2Name').value = 'فبراير 2025';
            document.getElementById('month3Name').value = 'مارس 2025';
            
            // تحديث البيانات
            updateMonthName('month1');
            setTimeout(() => updateMonthName('month2'), 100);
            setTimeout(() => updateMonthName('month3'), 200);
            
            updateStatus('تم حفظ البيانات التجريبية', 'success');
        }
        
        // تحميل البيانات المحفوظة
        function loadSavedData() {
            try {
                const saved = localStorage.getItem(`monthlyPlanning_${currentUser.id}`);
                if (saved) {
                    monthlyPlanning = JSON.parse(saved);
                    
                    // تحديث الحقول
                    document.getElementById('month1Name').value = monthlyPlanning.month1?.name || '';
                    document.getElementById('month2Name').value = monthlyPlanning.month2?.name || '';
                    document.getElementById('month3Name').value = monthlyPlanning.month3?.name || '';
                    
                    updateStatus('تم تحميل البيانات المحفوظة', 'success');
                    showCurrentData();
                } else {
                    updateStatus('لا توجد بيانات محفوظة', 'info');
                }
            } catch (error) {
                updateStatus('خطأ في تحميل البيانات: ' + error.message, 'error');
            }
        }
        
        // مسح البيانات
        function clearData() {
            monthlyPlanning = {};
            document.getElementById('month1Name').value = '';
            document.getElementById('month2Name').value = '';
            document.getElementById('month3Name').value = '';
            
            localStorage.removeItem(`monthlyPlanning_${currentUser.id}`);
            
            updateStatus('تم مسح جميع البيانات', 'info');
            showCurrentData();
        }
        
        // عرض البيانات الحالية
        function showCurrentData() {
            const display = document.getElementById('dataDisplay');
            display.textContent = JSON.stringify(monthlyPlanning, null, 2);
        }
        
        // تحديث رسالة الحالة
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // تهيئة تلقائية عند تحميل الصفحة
        window.onload = function() {
            initializeData();
            
            // محاولة تحميل البيانات المحفوظة
            setTimeout(loadSavedData, 500);
        };
        
        // اختبار دوري لفحص التغييرات
        setInterval(() => {
            const month1 = document.getElementById('month1Name').value;
            const month2 = document.getElementById('month2Name').value;
            const month3 = document.getElementById('month3Name').value;
            
            if (monthlyPlanning.month1?.name !== month1 ||
                monthlyPlanning.month2?.name !== month2 ||
                monthlyPlanning.month3?.name !== month3) {
                
                console.log('تم اكتشاف تغيير في الحقول');
            }
        }, 2000);
    </script>
</body>
</html>
