<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Tools - أدوات قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
        }
        .tool-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }
        .tool-section h3 {
            margin-top: 0;
            color: #2d3748;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #4299e1;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #3182ce;
        }
        .btn-success { background: #48bb78; }
        .btn-success:hover { background: #38a169; }
        .btn-warning { background: #ed8936; }
        .btn-warning:hover { background: #dd6b20; }
        .btn-danger { background: #f56565; }
        .btn-danger:hover { background: #e53e3e; }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: #edf2f7;
            border-left: 4px solid #4299e1;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-success { background: #48bb78; }
        .status-error { background: #f56565; }
        .status-warning { background: #ed8936; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ أدوات قاعدة البيانات</h1>
        
        <div class="tool-section">
            <h3>📊 فحص حالة قاعدة البيانات</h3>
            <p>تحقق من حالة الاتصال بقاعدة البيانات والجداول المطلوبة</p>
            <button class="btn btn-success" onclick="checkStatus()">فحص الحالة</button>
            <button class="btn" onclick="testConnection()">اختبار الاتصال</button>
            <button class="btn" onclick="detectCloudPanel()">كشف CloudPanel</button>
            <div id="status-result" class="result" style="display: none;"></div>
        </div>

        <div class="tool-section">
            <h3>☁️ إعداد CloudPanel</h3>
            <p>إعداد قاعدة البيانات لخوادم CloudPanel (أنشئ قاعدة البيانات في لوحة CloudPanel أولاً)</p>
            <input type="text" id="cloudDbName" placeholder="اسم قاعدة البيانات (مثل: username_csdb)" style="padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 4px; width: 200px;">
            <input type="text" id="cloudDbUser" placeholder="اسم المستخدم (مثل: username_user)" style="padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 4px; width: 200px;">
            <input type="password" id="cloudDbPass" placeholder="كلمة المرور" style="padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 4px; width: 200px;">
            <button class="btn btn-warning" onclick="setupCloudPanel()">إعداد CloudPanel</button>
            <div id="cloudpanel-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="tool-section">
            <h3>⚙️ إعداد قاعدة البيانات</h3>
            <p>إعداد قاعدة البيانات والمستخدم تلقائياً</p>
            <button class="btn btn-warning" onclick="autoSetup()">إعداد تلقائي</button>
            <button class="btn" onclick="manualSetup()">إعداد يدوي</button>
            <div id="setup-result" class="result" style="display: none;"></div>
        </div>

        <div class="tool-section">
            <h3>🔄 إعادة إنشاء قاعدة البيانات الكاملة</h3>
            <p>حذف وإعادة إنشاء قاعدة البيانات بالكامل مع جميع الجداول</p>
            <input type="password" id="recreatePassword" placeholder="كلمة مرور المدير (root)" style="padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 4px;">
            <button class="btn btn-danger" onclick="recreateDatabase()">إعادة إنشاء كاملة</button>
            <button class="btn btn-warning" onclick="recreateForce()">إعادة إنشاء (حذف الموجود)</button>
            <div id="recreate-result" class="result" style="display: none;"></div>
        </div>

        <div class="tool-section">
            <h3>📊 تهيئة بيانات API</h3>
            <p>إضافة البيانات الأساسية للأدوية واللقاحات ووسائل منع الحمل</p>
            <button class="btn btn-success" onclick="initializeApiData()">تهيئة البيانات</button>
            <div id="init-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="tool-section">
            <h3>🔧 إعداد متقدم</h3>
            <p>للإعداد اليدوي مع كلمة مرور المدير</p>
            <input type="password" id="rootPassword" placeholder="كلمة مرور المدير (root)" style="padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 4px;">
            <button class="btn btn-warning" onclick="setupWithPassword()">إعداد بكلمة المرور</button>
            <div id="manual-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="tool-section">
            <h3>🔄 إعادة تعيين الجلسة</h3>
            <p>حل مشاكل "المستخدم غير موجود" وأخطاء الجلسة</p>
            <button class="btn btn-warning" onclick="resetSession()">إعادة تعيين الجلسة</button>
            <button class="btn" onclick="clearBrowserData()">مسح بيانات المتصفح</button>
            <a href="force-clear-session.html" class="btn btn-danger">مسح شامل بالقوة</a>
            <div id="session-result" class="result" style="display: none;"></div>
        </div>

        <div class="tool-section">
            <h3>🔧 إصلاح مشاكل البيانات</h3>
            <p>حل مشاكل "لم يتم العثور على الطفل" وأخطاء تحميل البيانات</p>
            <button class="btn btn-warning" onclick="fixDataIssues()">إصلاح مشاكل البيانات</button>
            <button class="btn" onclick="debugUserLoading()">تشخيص تحميل المستخدمين</button>
            <div id="data-fix-result" class="result" style="display: none;"></div>
        </div>

        <div class="tool-section">
            <h3>🏠 العودة للموقع</h3>
            <a href="index.html" class="btn btn-success">العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <script>
        async function fetchData(url) {
            try {
                const response = await fetch(url);
                const data = await response.text();
                return data;
            } catch (error) {
                return JSON.stringify({error: error.message}, null, 2);
            }
        }

        async function checkStatus() {
            const result = document.getElementById('status-result');
            result.style.display = 'block';
            result.textContent = 'جاري فحص الحالة...';
            
            const data = await fetchData('db-status.php');
            result.textContent = data;
        }

        async function testConnection() {
            const result = document.getElementById('status-result');
            result.style.display = 'block';
            result.textContent = 'جاري اختبار الاتصال...';
            
            const data = await fetchData('test-db-connection.php');
            result.textContent = data;
        }

        async function autoSetup() {
            const result = document.getElementById('setup-result');
            result.style.display = 'block';
            result.textContent = 'جاري الإعداد التلقائي...';
            
            const data = await fetchData('auto-setup-db.php');
            result.textContent = data;
        }

        async function manualSetup() {
            const result = document.getElementById('setup-result');
            result.style.display = 'block';
            result.textContent = 'جاري الإعداد...';
            
            const data = await fetchData('setup-database.php?action=setup');
            result.textContent = data;
        }

        async function setupWithPassword() {
            const password = document.getElementById('rootPassword').value;
            if (!password) {
                alert('يرجى إدخال كلمة مرور المدير');
                return;
            }

            const result = document.getElementById('manual-result');
            result.style.display = 'block';
            result.textContent = 'جاري الإعداد بكلمة المرور...';

            const data = await fetchData(`setup-database.php?action=setup&root_password=${encodeURIComponent(password)}`);
            result.textContent = data;
        }

        async function recreateDatabase() {
            const password = document.getElementById('recreatePassword').value;
            if (!password) {
                alert('يرجى إدخال كلمة مرور المدير لإعادة إنشاء قاعدة البيانات');
                return;
            }

            if (!confirm('هل أنت متأكد من إعادة إنشاء قاعدة البيانات؟ سيتم فقدان جميع البيانات الحالية!')) {
                return;
            }

            const result = document.getElementById('recreate-result');
            result.style.display = 'block';
            result.textContent = 'جاري إعادة إنشاء قاعدة البيانات...';

            const data = await fetchData(`recreate-database.php?action=recreate&root_password=${encodeURIComponent(password)}`);
            result.textContent = data;
        }

        async function recreateForce() {
            const password = document.getElementById('recreatePassword').value;
            if (!password) {
                alert('يرجى إدخال كلمة مرور المدير لإعادة إنشاء قاعدة البيانات');
                return;
            }

            if (!confirm('هل أنت متأكد من حذف وإعادة إنشاء قاعدة البيانات بالكامل؟ سيتم فقدان جميع البيانات نهائياً!')) {
                return;
            }

            const result = document.getElementById('recreate-result');
            result.style.display = 'block';
            result.textContent = 'جاري حذف وإعادة إنشاء قاعدة البيانات...';

            const data = await fetchData(`recreate-database.php?action=recreate&root_password=${encodeURIComponent(password)}&force=true`);
            result.textContent = data;
        }

        async function initializeApiData() {
            const result = document.getElementById('init-result');
            result.style.display = 'block';
            result.textContent = 'جاري تهيئة بيانات API...';

            const data = await fetchData('initialize-api-data.php');
            result.textContent = data;
        }

        async function detectCloudPanel() {
            const result = document.getElementById('status-result');
            result.style.display = 'block';
            result.textContent = 'جاري كشف بيئة CloudPanel...';

            const data = await fetchData('cloudpanel-setup.php?action=detect');
            result.textContent = data;
        }

        async function setupCloudPanel() {
            const dbName = document.getElementById('cloudDbName').value;
            const dbUser = document.getElementById('cloudDbUser').value;
            const dbPass = document.getElementById('cloudDbPass').value;

            if (!dbName || !dbUser || !dbPass) {
                alert('يرجى إدخال جميع بيانات قاعدة البيانات');
                return;
            }

            const result = document.getElementById('cloudpanel-result');
            result.style.display = 'block';
            result.textContent = 'جاري إعداد قاعدة البيانات في CloudPanel...';

            const url = `cloudpanel-setup.php?action=setup&db_name=${encodeURIComponent(dbName)}&db_user=${encodeURIComponent(dbUser)}&db_pass=${encodeURIComponent(dbPass)}`;
            const data = await fetchData(url);
            result.textContent = data;
        }

        async function resetSession() {
            const result = document.getElementById('session-result');
            result.style.display = 'block';
            result.textContent = 'جاري إعادة تعيين الجلسة...';

            try {
                // Clear browser storage
                localStorage.clear();
                sessionStorage.clear();

                // Call server-side reset
                const data = await fetchData('reset-session.php');
                result.textContent = data + '\n\n✅ تم مسح بيانات المتصفح المحلية';

                // Reload page after delay
                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);

            } catch (error) {
                result.textContent = 'خطأ في إعادة تعيين الجلسة: ' + error.message;
            }
        }

        function clearBrowserData() {
            const result = document.getElementById('session-result');
            result.style.display = 'block';
            result.textContent = 'جاري مسح بيانات المتصفح...';

            try {
                // Clear all browser storage
                localStorage.clear();
                sessionStorage.clear();

                // Clear cookies (best effort)
                document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });

                result.textContent = '✅ تم مسح جميع بيانات المتصفح\n\nسيتم إعادة تحميل الصفحة...';

                // Reload page
                setTimeout(() => {
                    window.location.reload();
                }, 2000);

            } catch (error) {
                result.textContent = 'خطأ في مسح بيانات المتصفح: ' + error.message;
            }
        }
    </script>
</body>
</html>
