<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ اختبار نهائي للإشعارات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #28a745; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .instructions {
            background: #e7f3ff;
            border: 2px solid #007bff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .expected-behavior {
            background: #d4edda;
            border: 2px solid #28a745;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .not-expected {
            background: #f8d7da;
            border: 2px solid #dc3545;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.save { background: #28a745; }
        button.save:hover { background: #218838; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>✅ اختبار نهائي لإصلاح الإشعارات</h1>
    
    <div class="instructions">
        <h2>📋 تعليمات الاختبار النهائي</h2>
        <ol>
            <li><strong>افتح الصفحة الأصلية:</strong> <a href="cs-manager.html" target="_blank">cs-manager.html</a></li>
            <li><strong>اذهب لصفحة:</strong> "💉 تدبير مخزون اللقاحات"</li>
            <li><strong>قم بالاختبارات التالية:</strong></li>
        </ol>
    </div>
    
    <div class="expected-behavior">
        <h3>✅ السلوك المتوقع (لا إشعارات)</h3>
        <ul>
            <li>🔕 تغيير كميات اللقاحات في الحقول</li>
            <li>🔕 تغيير أسماء الأشهر</li>
            <li>🔕 الانتقال بين الحقول</li>
            <li>🔕 إعادة تحميل الصفحة (refresh)</li>
            <li>🔕 حساب المجموع الإجمالي تلقائياً</li>
        </ul>
        <p><strong>النتيجة:</strong> يجب أن تتم جميع العمليات بصمت بدون إشعارات</p>
    </div>
    
    <div class="test-section">
        <h3>🔔 السلوك المتوقع (مع إشعارات)</h3>
        <ul>
            <li>✅ الضغط على زر "💾 حفظ التخطيط الشهري"</li>
            <li>✅ رسالة: "تم حفظ التخطيط الشهري بنجاح"</li>
        </ul>
        <p><strong>النتيجة:</strong> إشعار واحد فقط عند الحفظ اليدوي</p>
    </div>
    
    <div class="not-expected">
        <h3>❌ السلوك غير المرغوب (يجب عدم حدوثه)</h3>
        <ul>
            <li>❌ "تم حفظ مخزون اللقاحات بنجاح" عند التغيير</li>
            <li>❌ إشعارات متكررة عند الكتابة</li>
            <li>❌ إشعارات عند حساب المجموع</li>
            <li>❌ إشعارات عند تحديث العرض</li>
        </ul>
        <p><strong>إذا ظهرت هذه الإشعارات:</strong> هناك مشكلة تحتاج إصلاح</p>
    </div>
    
    <div class="test-section">
        <h2>🧪 اختبار سريع</h2>
        <p>يمكنك اختبار الإشعارات هنا أيضاً:</p>
        <button onclick="testSilentAction()">🔕 عمل صامت (لا إشعار)</button>
        <button onclick="testNotificationAction()" class="save">🔔 عمل مع إشعار</button>
        <button onclick="clearTestResults()">🗑️ مسح النتائج</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز للاختبار النهائي
    </div>
    
    <div class="test-section">
        <h3>📊 نتائج الاختبار</h3>
        <div id="testResults">
            <p>لم يتم تشغيل أي اختبار بعد...</p>
        </div>
    </div>

    <script>
        let testCount = 0;
        
        // تخصيص دالة showToast لتسجيل الإشعارات
        function showToast(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const results = document.getElementById('testResults');
            
            const notification = document.createElement('div');
            notification.style.cssText = `
                padding: 10px; margin: 5px 0; border-radius: 5px;
                background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
                border-left: 4px solid ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
            `;
            notification.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            
            results.appendChild(notification);
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
        }
        
        function testSilentAction() {
            testCount++;
            
            // محاكاة عمل صامت (مثل تغيير كمية لقاح)
            console.log(`🔕 عمل صامت #${testCount}: تغيير كمية HB1 إلى 50`);
            
            updateStatus(`تم تنفيذ عمل صامت #${testCount} - يجب عدم ظهور إشعار`, 'info');
            
            // لا نستدعي showToast هنا لأنه عمل صامت
        }
        
        function testNotificationAction() {
            testCount++;
            
            // محاكاة عمل مع إشعار (مثل الحفظ اليدوي)
            console.log(`🔔 عمل مع إشعار #${testCount}: حفظ يدوي`);
            
            showToast(`تم حفظ التخطيط الشهري بنجاح (اختبار #${testCount})`, 'success');
            updateStatus(`تم تنفيذ عمل مع إشعار #${testCount} - يجب ظهور إشعار`, 'pass');
        }
        
        function clearTestResults() {
            document.getElementById('testResults').innerHTML = '<p>تم مسح نتائج الاختبار...</p>';
            testCount = 0;
            updateStatus('تم مسح نتائج الاختبار', 'info');
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            updateStatus('جاهز للاختبار النهائي - اتبع التعليمات أعلاه', 'info');
        };
    </script>
</body>
</html>
