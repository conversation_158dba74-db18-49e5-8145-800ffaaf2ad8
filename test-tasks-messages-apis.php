<?php
/**
 * Comprehensive Test for Tasks and Messages APIs
 * اختبار شامل لـ APIs المهام والرسائل
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'api_tests' => [],
    'functionality_tests' => [],
    'success' => false
];

// Test 1: Basic API connectivity
$basic_tests = [
    'tasks-api.php' => [
        'test' => ['action' => 'test'],
        'load' => ['action' => 'load', 'user_id' => 'admin_001']
    ],
    'messages-api.php' => [
        'test' => ['action' => 'test'],
        'load' => ['action' => 'load', 'user_id' => 'admin_001']
    ]
];

foreach ($basic_tests as $api_file => $tests) {
    $response['api_tests'][$api_file] = [];
    
    foreach ($tests as $test_name => $test_data) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/$api_file");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $result = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $api_result = json_decode($result, true);
            $status = ($http_code == 200 && $api_result && isset($api_result['success'])) ? 
                ($api_result['success'] ? 'SUCCESS' : 'API_ERROR') : 'HTTP_ERROR';
            
            $response['api_tests'][$api_file][$test_name] = [
                'status' => $status,
                'http_code' => $http_code,
                'response' => $api_result,
                'error' => $status !== 'SUCCESS' ? ($api_result['message'] ?? 'Unknown error') : null
            ];
            
        } catch (Exception $e) {
            $response['api_tests'][$api_file][$test_name] = [
                'status' => 'EXCEPTION',
                'error' => $e->getMessage()
            ];
        }
    }
}

// Test 2: Functionality tests (save/send operations)
try {
    require_once 'config/database-live.php';
    $pdo = getDatabaseConnection();
    
    // Test Tasks functionality
    $response['functionality_tests']['tasks'] = [];
    
    // Test saving a task
    $task_data = [
        'action' => 'save',
        'user_id' => 'admin_001',
        'title' => 'مهمة اختبار',
        'description' => 'هذه مهمة اختبار للتحقق من عمل النظام',
        'priority' => 'medium',
        'due_date' => date('Y-m-d', strtotime('+7 days'))
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/tasks-api.php");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($task_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $task_result = json_decode($result, true);
    $response['functionality_tests']['tasks']['save_task'] = [
        'status' => ($http_code == 200 && $task_result && $task_result['success']) ? 'SUCCESS' : 'FAILED',
        'response' => $task_result
    ];
    
    // Test Messages functionality
    $response['functionality_tests']['messages'] = [];
    
    // Test sending a message
    $message_data = [
        'action' => 'send',
        'sender_id' => 'admin_001',
        'receiver_id' => 'nurse_001',
        'message' => 'رسالة اختبار من النظام'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/messages-api.php");
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $message_result = json_decode($result, true);
    $response['functionality_tests']['messages']['send_message'] = [
        'status' => ($http_code == 200 && $message_result && $message_result['success']) ? 'SUCCESS' : 'FAILED',
        'response' => $message_result
    ];
    
    // Check database tables
    $response['database_check'] = [];
    
    // Check tasks table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM tasks");
    $tasks_count = $stmt->fetch()['count'];
    $response['database_check']['tasks_table'] = [
        'exists' => true,
        'record_count' => $tasks_count
    ];
    
    // Check messages table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM messages");
    $messages_count = $stmt->fetch()['count'];
    $response['database_check']['messages_table'] = [
        'exists' => true,
        'record_count' => $messages_count
    ];
    
} catch (Exception $e) {
    $response['functionality_tests']['error'] = $e->getMessage();
}

// Summary
$successful_tests = 0;
$total_tests = 0;

foreach ($response['api_tests'] as $api => $tests) {
    foreach ($tests as $test => $result) {
        $total_tests++;
        if ($result['status'] === 'SUCCESS') {
            $successful_tests++;
        }
    }
}

foreach ($response['functionality_tests'] as $category => $tests) {
    if (is_array($tests)) {
        foreach ($tests as $test => $result) {
            $total_tests++;
            if ($result['status'] === 'SUCCESS') {
                $successful_tests++;
            }
        }
    }
}

$response['summary'] = [
    'total_tests' => $total_tests,
    'successful_tests' => $successful_tests,
    'success_rate' => $total_tests > 0 ? round(($successful_tests / $total_tests) * 100, 1) . '%' : '0%',
    'apis_tested' => ['tasks-api.php', 'messages-api.php']
];

$response['success'] = $successful_tests >= ($total_tests * 0.8); // 80% success rate
$response['message'] = $response['success'] ? 
    'Tasks and Messages APIs are working correctly' : 
    'Some issues found with Tasks/Messages APIs';

if ($response['success']) {
    $response['next_steps'] = [
        'APIs are ready for use',
        'Test task creation and messaging in the main application',
        'All data will be saved to MySQL database'
    ];
} else {
    $response['recommendations'] = [
        'Check database table structures',
        'Verify API endpoints are accessible',
        'Test individual API actions manually'
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
