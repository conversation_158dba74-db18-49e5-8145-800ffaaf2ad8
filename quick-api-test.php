<?php
/**
 * Quick API Test - Fast verification of critical endpoints
 * اختبار سريع للـ APIs الأساسية
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'test_type' => 'QUICK_API_TEST',
    'results' => [],
    'summary' => [],
    'success' => false
];

// Critical APIs to test
$critical_apis = [
    'user-api-simple.php' => [
        'name' => 'User Management',
        'action' => 'test',
        'priority' => 'CRITICAL'
    ],
    'children-api.php' => [
        'name' => 'Children Registry',
        'action' => 'load',
        'priority' => 'CRITICAL'
    ],
    'medicines-api.php' => [
        'name' => 'Medicine Management',
        'action' => 'test',
        'priority' => 'CRITICAL'
    ],
    'vaccines-api.php' => [
        'name' => 'Vaccine Management',
        'action' => 'load_vaccines',
        'priority' => 'CRITICAL'
    ],
    'family-planning-api.php' => [
        'name' => 'Family Planning',
        'action' => 'test',
        'priority' => 'CRITICAL'
    ]
];

$working_apis = 0;
$total_response_time = 0;

foreach ($critical_apis as $api_file => $config) {
    $start_time = microtime(true);
    
    try {
        $test_data = [
            'action' => $config['action'],
            'user_id' => 'test_user_001'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/$api_file");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        $end_time = microtime(true);
        $response_time = round(($end_time - $start_time) * 1000, 2);
        $total_response_time += $response_time;
        
        $api_result = [
            'api' => $api_file,
            'name' => $config['name'],
            'priority' => $config['priority'],
            'response_time' => $response_time,
            'status' => 'UNKNOWN',
            'message' => '',
            'working' => false
        ];
        
        if ($curl_error) {
            $api_result['status'] = 'CURL_ERROR';
            $api_result['message'] = $curl_error;
        } elseif ($http_code !== 200) {
            $api_result['status'] = 'HTTP_ERROR';
            $api_result['message'] = "HTTP $http_code";
        } else {
            $api_response = json_decode($result, true);
            if ($api_response && isset($api_response['success'])) {
                if ($api_response['success']) {
                    $api_result['status'] = 'SUCCESS';
                    $api_result['message'] = 'API working correctly';
                    $api_result['working'] = true;
                    $working_apis++;
                } else {
                    $api_result['status'] = 'API_ERROR';
                    $api_result['message'] = $api_response['message'] ?? 'Unknown API error';
                }
            } else {
                $api_result['status'] = 'INVALID_RESPONSE';
                $api_result['message'] = 'Invalid JSON response';
            }
        }
        
        $response['results'][] = $api_result;
        
    } catch (Exception $e) {
        $response['results'][] = [
            'api' => $api_file,
            'name' => $config['name'],
            'priority' => $config['priority'],
            'status' => 'EXCEPTION',
            'message' => $e->getMessage(),
            'working' => false,
            'response_time' => 0
        ];
    }
}

// Test database connection
$db_status = 'UNKNOWN';
$db_message = '';
try {
    require_once 'config/database-live.php';
    $pdo = getDatabaseConnection();
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $user_count = $stmt->fetch()['count'];
    $db_status = 'SUCCESS';
    $db_message = "Connected successfully, $user_count users found";
} catch (Exception $e) {
    $db_status = 'FAILED';
    $db_message = $e->getMessage();
}

// Calculate summary
$total_apis = count($critical_apis);
$success_rate = round(($working_apis / $total_apis) * 100, 1);
$avg_response_time = $total_apis > 0 ? round($total_response_time / $total_apis, 2) : 0;

$response['summary'] = [
    'total_apis_tested' => $total_apis,
    'working_apis' => $working_apis,
    'success_rate' => $success_rate . '%',
    'average_response_time' => $avg_response_time . 'ms',
    'database_status' => $db_status,
    'database_message' => $db_message
];

// Determine overall success
$response['success'] = ($working_apis >= ($total_apis * 0.8)) && ($db_status === 'SUCCESS');

if ($response['success']) {
    $response['message'] = 'System is working well - all critical APIs are functional';
    $response['status'] = 'OPERATIONAL';
} else {
    $response['message'] = 'System has issues that need attention';
    $response['status'] = 'NEEDS_ATTENTION';
    
    // Add specific recommendations
    $response['recommendations'] = [];
    
    if ($db_status !== 'SUCCESS') {
        $response['recommendations'][] = 'Fix database connection issues';
    }
    
    if ($working_apis < $total_apis) {
        $failed_apis = array_filter($response['results'], function($api) {
            return !$api['working'];
        });
        $failed_names = array_column($failed_apis, 'name');
        $response['recommendations'][] = 'Fix these APIs: ' . implode(', ', $failed_names);
    }
    
    if ($avg_response_time > 1000) {
        $response['recommendations'][] = 'Optimize API performance - response times are slow';
    }
}

// Add next steps
if ($response['success']) {
    $response['next_steps'] = [
        'System is ready for use',
        'Visit https://www.csmanager.online/ to start using the system',
        'Login with: admin / password',
        'Run comprehensive test for detailed analysis'
    ];
} else {
    $response['next_steps'] = [
        'Run comprehensive-system-test.php for detailed diagnosis',
        'Check database configuration in config/database-live.php',
        'Verify API files are accessible',
        'Contact system administrator if issues persist'
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
