<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // تضمين إعدادات قاعدة البيانات
    require_once 'config/database-live.php';
    
    // الاتصال بقاعدة البيانات
    $pdo = getDatabaseConnection();
    
    // قراءة البيانات المرسلة
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        $input = $_POST;
    }

    $action = $input['action'] ?? $_GET['action'] ?? '';

    if (empty($action)) {
        throw new Exception('لم يتم تحديد الإجراء المطلوب');
    }

    switch ($action) {
        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'User API يعمل بنجاح',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'load_all_users':
            $stmt = $pdo->prepare("
                SELECT u.id, u.username, u.name, u.center_id, u.role,
                       u.created_at, u.updated_at, u.is_active,
                       c.name as center_name
                FROM users u
                LEFT JOIN centers c ON u.center_id = c.id
                WHERE u.is_active = TRUE
                ORDER BY u.name
            ");
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'users' => $users,
                'count' => count($users),
                'timestamp' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'authenticate_user':
            $username = $input['username'] ?? '';
            $password = $input['password'] ?? '';

            if (empty($username) || empty($password)) {
                throw new Exception('اسم المستخدم وكلمة المرور مطلوبان');
            }

            $stmt = $pdo->prepare("
                SELECT u.id, u.username, u.password, u.name, u.center_id, u.role,
                       c.name as center_name
                FROM users u
                LEFT JOIN centers c ON u.center_id = c.id
                WHERE u.username = ? AND u.is_active = TRUE
            ");
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user && password_verify($password, $user['password'])) {
                // تحديث وقت آخر دخول
                $updateStmt = $pdo->prepare("UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?");
                $updateStmt->execute([$user['id']]);

                // إزالة كلمة المرور من الاستجابة
                unset($user['password']);

                echo json_encode([
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'user' => $user,
                    'timestamp' => date('Y-m-d H:i:s')
                ], JSON_UNESCAPED_UNICODE);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة',
                    'timestamp' => date('Y-m-d H:i:s')
                ], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        default:
            throw new Exception('إجراء غير صحيح: ' . $action);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    error_log('خطأ في قاعدة البيانات user-api-simple.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error_type' => 'database',
        'file' => 'user-api-simple.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    error_log('خطأ في user-api-simple.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في User API: ' . $e->getMessage(),
        'error_type' => 'general',
        'file' => 'user-api-simple.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
