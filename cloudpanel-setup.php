<?php
/**
 * CloudPanel Database Setup Script
 * سكريبت إعداد قاعدة البيانات لـ CloudPanel
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$action = $_GET['action'] ?? 'detect';
$db_name = $_GET['db_name'] ?? '';
$db_user = $_GET['db_user'] ?? '';
$db_pass = $_GET['db_pass'] ?? '';

$response = [
    'success' => false,
    'message' => '',
    'action' => $action,
    'timestamp' => date('Y-m-d H:i:s'),
    'cloudpanel_detected' => false,
    'available_databases' => []
];

// Detect CloudPanel environment
$response['cloudpanel_detected'] = (
    isset($_SERVER['HTTP_HOST']) && 
    (strpos($_SERVER['HTTP_HOST'], 'csmanager.online') !== false ||
     file_exists('/home/<USER>') || 
     file_exists('/opt/clp'))
);

try {
    switch ($action) {
        case 'detect':
            // Try to detect existing databases
            $test_configs = [
                ['host' => 'localhost', 'user' => 'root', 'pass' => ''],
                ['host' => 'localhost', 'user' => 'root', 'pass' => 'root'],
            ];
            
            foreach ($test_configs as $config) {
                try {
                    $pdo = new PDO("mysql:host={$config['host']};charset=utf8mb4", $config['user'], $config['pass'], [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    ]);
                    
                    // Get list of databases
                    $stmt = $pdo->query("SHOW DATABASES");
                    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    
                    // Filter relevant databases
                    $relevant_dbs = array_filter($databases, function($db) {
                        return (strpos($db, 'csmanage') !== false || 
                                strpos($db, 'csdb') !== false ||
                                strpos($db, 'healthcare') !== false);
                    });
                    
                    $response['available_databases'] = array_values($relevant_dbs);
                    $response['root_access'] = true;
                    break;
                    
                } catch (PDOException $e) {
                    continue;
                }
            }
            
            $response['success'] = true;
            $response['message'] = 'CloudPanel environment detected. Found ' . count($response['available_databases']) . ' relevant databases.';
            $response['instructions'] = [
                '1. Create database in CloudPanel if not exists',
                '2. Note the exact database name and user CloudPanel creates',
                '3. Use setup action with those credentials',
                '4. Example: cloudpanel-setup.php?action=setup&db_name=yourprefix_csdb&db_user=yourprefix_user&db_pass=yourpassword'
            ];
            break;
            
        case 'setup':
            if (empty($db_name) || empty($db_user) || empty($db_pass)) {
                throw new Exception('Database name, user, and password are required for setup');
            }
            
            // Test connection with provided credentials
            $pdo = new PDO("mysql:host=localhost;dbname={$db_name};charset=utf8mb4", $db_user, $db_pass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
            
            $response['connection_test'] = 'SUCCESS';
            
            // Read and execute database schema
            $schema_file = __DIR__ . '/database_design.sql';
            if (!file_exists($schema_file)) {
                throw new Exception('Database schema file not found');
            }
            
            $schema_sql = file_get_contents($schema_file);
            
            // Remove database creation commands (already exists in CloudPanel)
            $schema_sql = preg_replace('/CREATE DATABASE.*?;/i', '', $schema_sql);
            $schema_sql = preg_replace('/USE.*?;/i', '', $schema_sql);
            
            // Execute schema
            $queries = explode(';', $schema_sql);
            $executed = 0;
            $errors = [];
            
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query)) {
                    try {
                        $pdo->exec($query);
                        $executed++;
                    } catch (PDOException $e) {
                        $errors[] = "Query error: " . substr($query, 0, 50) . "... - " . $e->getMessage();
                    }
                }
            }
            
            // Update configuration file
            $config_update = "<?php\n";
            $config_update .= "// CloudPanel Database Configuration - Auto-generated\n";
            $config_update .= "// Generated on: " . date('Y-m-d H:i:s') . "\n\n";
            $config_update .= "\$cloudpanel_db_config = [\n";
            $config_update .= "    'host' => 'localhost',\n";
            $config_update .= "    'dbname' => '{$db_name}',\n";
            $config_update .= "    'username' => '{$db_user}',\n";
            $config_update .= "    'password' => '{$db_pass}',\n";
            $config_update .= "    'charset' => 'utf8mb4'\n";
            $config_update .= "];\n";
            
            file_put_contents(__DIR__ . '/config/cloudpanel-config.php', $config_update);
            
            // Count tables
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $response['success'] = true;
            $response['message'] = 'CloudPanel database setup completed';
            $response['executed_queries'] = $executed;
            $response['tables_created'] = count($tables);
            $response['table_list'] = $tables;
            $response['errors'] = $errors;
            $response['config_saved'] = 'config/cloudpanel-config.php';
            
            break;
            
        case 'test':
            // Test current configuration
            require_once 'config/database-live.php';
            $pdo = getDatabaseConnection();
            $stmt = $pdo->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = DATABASE()");
            $result = $stmt->fetch();
            
            $response['success'] = true;
            $response['message'] = 'Database connection working';
            $response['table_count'] = $result['table_count'];
            break;
            
        default:
            throw new Exception('Invalid action. Use: detect, setup, or test');
    }
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    $response['error'] = true;
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
