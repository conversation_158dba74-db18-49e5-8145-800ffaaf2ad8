<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔌 اختبار اتصال قاعدة البيانات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .success-section { border-color: #28a745; }
        .error-section { border-color: #dc3545; }
        .warning-section { border-color: #ffc107; }
        button { 
            padding: 15px 30px; margin: 10px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
            font-size: 16px; font-weight: bold;
        }
        button:hover { background: #0056b3; }
        button.test { background: #17a2b8; }
        button.test:hover { background: #138496; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { 
            background: #f8f9fa; padding: 15px; border-radius: 5px; 
            overflow-x: auto; font-size: 12px; white-space: pre-wrap;
            max-height: 400px; overflow-y: auto;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .api-test {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        .api-test h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <h1>🔌 اختبار اتصال قاعدة البيانات</h1>
    
    <div class="test-section error-section">
        <h2>❌ المشكلة المكتشفة</h2>
        <p><strong>خطأ الاتصال:</strong> Access denied for user 'csdbuser'@'localhost'</p>
        <p><strong>السبب:</strong> medicines-api.php لا يستطيع الاتصال بقاعدة البيانات</p>
        <p><strong>الحل:</strong> اختبار إعدادات مختلفة للاتصال</p>
    </div>
    
    <div class="test-section">
        <h2>🧪 اختبارات الاتصال</h2>
        <button onclick="testVaccinesAPI()" class="test">🔍 اختبار vaccines-api.php</button>
        <button onclick="testMedicinesAPI()" class="test">🔍 اختبار medicines-api.php</button>
        <button onclick="testBothAPIs()" class="test">🔍 اختبار كلا الـ APIs</button>
        <button onclick="clearResults()">🗑️ مسح النتائج</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز لاختبار اتصال قاعدة البيانات
    </div>
    
    <div class="comparison">
        <div class="api-test">
            <h4>💉 vaccines-api.php</h4>
            <div id="vaccinesResult">لم يتم الاختبار بعد</div>
        </div>
        
        <div class="api-test">
            <h4>💊 medicines-api.php</h4>
            <div id="medicinesResult">لم يتم الاختبار بعد</div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📋 تفاصيل الاختبارات</h2>
        <pre id="detailedResults">لا توجد نتائج بعد</pre>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function addResult(message) {
            const results = document.getElementById('detailedResults');
            const timestamp = new Date().toLocaleTimeString();
            results.textContent += `[${timestamp}] ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }
        
        async function testVaccinesAPI() {
            updateStatus('جاري اختبار vaccines-api.php...', 'info');
            addResult('بدء اختبار vaccines-api.php');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'test',
                        user_id: 'test_user'
                    })
                });
                
                const responseText = await response.text();
                addResult(`vaccines-api.php استجابة: ${response.status} - ${responseText}`);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        document.getElementById('vaccinesResult').innerHTML = `
                            <div style="color: green;">✅ يعمل بشكل صحيح</div>
                            <div>الرسالة: ${data.message}</div>
                            <div>الوقت: ${data.timestamp}</div>
                        `;
                        updateStatus('vaccines-api.php يعمل بشكل صحيح', 'pass');
                    } catch (e) {
                        document.getElementById('vaccinesResult').innerHTML = `
                            <div style="color: orange;">⚠️ يعمل لكن استجابة غير صالحة</div>
                            <div>الاستجابة: ${responseText.substring(0, 100)}...</div>
                        `;
                        updateStatus('vaccines-api.php استجابة غير صالحة', 'warning');
                    }
                } else {
                    document.getElementById('vaccinesResult').innerHTML = `
                        <div style="color: red;">❌ خطأ HTTP ${response.status}</div>
                        <div>الاستجابة: ${responseText.substring(0, 100)}...</div>
                    `;
                    updateStatus(`vaccines-api.php خطأ ${response.status}`, 'fail');
                }
                
            } catch (error) {
                addResult(`خطأ في vaccines-api.php: ${error.message}`);
                document.getElementById('vaccinesResult').innerHTML = `
                    <div style="color: red;">❌ خطأ في الشبكة</div>
                    <div>الخطأ: ${error.message}</div>
                `;
                updateStatus('خطأ في الشبكة مع vaccines-api.php', 'fail');
            }
        }
        
        async function testMedicinesAPI() {
            updateStatus('جاري اختبار medicines-api.php...', 'info');
            addResult('بدء اختبار medicines-api.php');
            
            try {
                const response = await fetch('medicines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'test',
                        user_id: 'test_user'
                    })
                });
                
                const responseText = await response.text();
                addResult(`medicines-api.php استجابة: ${response.status} - ${responseText}`);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        
                        let statusColor = 'green';
                        let statusText = '✅ يعمل بشكل صحيح';
                        
                        if (!data.success) {
                            statusColor = 'orange';
                            statusText = '⚠️ مشكلة في قاعدة البيانات';
                        }
                        
                        document.getElementById('medicinesResult').innerHTML = `
                            <div style="color: ${statusColor};">${statusText}</div>
                            <div>الرسالة: ${data.message}</div>
                            <div>قاعدة البيانات: ${data.database || 'غير محدد'}</div>
                            <div>الوقت: ${data.timestamp}</div>
                            ${data.database_error ? `<div style="color: red;">خطأ DB: ${data.database_error}</div>` : ''}
                        `;
                        
                        if (data.success) {
                            updateStatus('medicines-api.php يعمل بشكل صحيح', 'pass');
                        } else {
                            updateStatus('medicines-api.php مشكلة في قاعدة البيانات', 'warning');
                        }
                    } catch (e) {
                        document.getElementById('medicinesResult').innerHTML = `
                            <div style="color: orange;">⚠️ يعمل لكن استجابة غير صالحة</div>
                            <div>الاستجابة: ${responseText.substring(0, 100)}...</div>
                        `;
                        updateStatus('medicines-api.php استجابة غير صالحة', 'warning');
                    }
                } else {
                    document.getElementById('medicinesResult').innerHTML = `
                        <div style="color: red;">❌ خطأ HTTP ${response.status}</div>
                        <div>الاستجابة: ${responseText.substring(0, 100)}...</div>
                    `;
                    updateStatus(`medicines-api.php خطأ ${response.status}`, 'fail');
                }
                
            } catch (error) {
                addResult(`خطأ في medicines-api.php: ${error.message}`);
                document.getElementById('medicinesResult').innerHTML = `
                    <div style="color: red;">❌ خطأ في الشبكة</div>
                    <div>الخطأ: ${error.message}</div>
                `;
                updateStatus('خطأ في الشبكة مع medicines-api.php', 'fail');
            }
        }
        
        async function testBothAPIs() {
            updateStatus('جاري اختبار كلا الـ APIs...', 'info');
            addResult('=== بدء اختبار شامل ===');
            
            await testVaccinesAPI();
            await new Promise(resolve => setTimeout(resolve, 1000)); // انتظار ثانية
            await testMedicinesAPI();
            
            addResult('=== انتهاء الاختبار الشامل ===');
            updateStatus('تم اختبار كلا الـ APIs', 'info');
        }
        
        function clearResults() {
            document.getElementById('detailedResults').textContent = 'تم مسح النتائج';
            document.getElementById('vaccinesResult').textContent = 'لم يتم الاختبار بعد';
            document.getElementById('medicinesResult').textContent = 'لم يتم الاختبار بعد';
            updateStatus('تم مسح النتائج', 'info');
        }
        
        // تشغيل اختبار تلقائي عند التحميل
        window.onload = function() {
            updateStatus('جاهز لاختبار اتصال قاعدة البيانات', 'info');
            addResult('تم تحميل صفحة اختبار الاتصال');
        };
    </script>
</body>
</html>
