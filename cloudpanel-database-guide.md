# CloudPanel Database Setup Guide
# دليل إعداد قاعدة البيانات في CloudPanel

## 🌐 CloudPanel-Specific Instructions

### Step 1: Create Database in CloudPanel Dashboard

1. **Login to CloudPanel**
   - Go to your CloudPanel dashboard
   - Navigate to your site

2. **Create Database**
   - Go to **Databases** section
   - Click **Add Database**
   - **Database Name**: Enter `csdb` (CloudPanel may prefix it)
   - **Database User**: Enter `csdbuser` (CloudPanel may prefix it)
   - **Password**: Use `j5aKN6lz5bsujTcWaYAd` or generate a strong one
   - Click **Create**

3. **Note the Actual Names**
   - CloudPanel often adds prefixes like `yourusername_csdb`
   - Write down the exact database name and username created

### Step 2: Detect Current Setup

Visit: `cloudpanel-setup.php?action=detect`

This will:
- Detect if you're on CloudPanel
- Show available databases
- Provide setup instructions

### Step 3: Setup Database Tables

Use one of these methods:

#### Method A: Web Interface
1. Open `database-tools.html`
2. Use the CloudPanel section (if available)
3. Enter your CloudPanel database credentials

#### Method B: Direct API Call
```
cloudpanel-setup.php?action=setup&db_name=YOUR_DB_NAME&db_user=YOUR_DB_USER&db_pass=YOUR_PASSWORD
```

Replace:
- `YOUR_DB_NAME` with the actual database name from CloudPanel
- `YOUR_DB_USER` with the actual username from CloudPanel  
- `YOUR_PASSWORD` with the password you set

### Step 4: Initialize Data

After setup, run:
```
initialize-api-data.php
```

## 📋 Common CloudPanel Database Names

CloudPanel typically creates databases with these patterns:
- `username_csdb`
- `sitename_csdb`
- `domain_csdb`

And users like:
- `username_csdbuser`
- `sitename_user`
- `domain_user`

## 🔧 Example Setup Commands

If your CloudPanel created:
- Database: `csmanage_csdb`
- User: `csmanage_user`
- Password: `your_password`

Then use:
```
cloudpanel-setup.php?action=setup&db_name=csmanage_csdb&db_user=csmanage_user&db_pass=your_password
```

## ✅ Verification

After setup:
1. Visit `db-status.php` - Should show connection success
2. Visit your main site - API errors should be gone
3. Check `cloudpanel-setup.php?action=test`

## 🚨 Troubleshooting

### "Database not found"
- Make sure you created the database in CloudPanel first
- Use the exact name CloudPanel shows

### "Access denied"
- Verify username and password
- Check if CloudPanel added prefixes to the username

### "Connection refused"
- Ensure MySQL is running (usually automatic in CloudPanel)
- Check if your hosting plan includes database access

## 📱 Quick Setup Summary

1. **CloudPanel Dashboard** → Create database
2. **Note exact names** CloudPanel creates
3. **Run**: `cloudpanel-setup.php?action=setup&db_name=EXACT_NAME&db_user=EXACT_USER&db_pass=PASSWORD`
4. **Run**: `initialize-api-data.php`
5. **Test**: Visit your main website

## 🔄 Alternative: Manual Configuration

If automatic setup fails, you can manually update `config/database-live.php`:

```php
$db_configs = [
    [
        'host' => 'localhost',
        'dbname' => 'your_actual_db_name',
        'username' => 'your_actual_username', 
        'password' => 'your_actual_password',
        'charset' => 'utf8mb4'
    ]
];
```

---

**Note**: CloudPanel manages database permissions automatically, so you don't need root access to MySQL server.
