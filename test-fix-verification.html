<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ التحقق من الإصلاح</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #28a745; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #28a745;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #218838; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { 
            background: #f8f9fa; padding: 15px; border-radius: 5px; 
            overflow-x: auto; font-size: 12px; white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>✅ التحقق من إصلاح vaccines-api.php</h1>
    
    <div class="test-section">
        <h2>🧪 اختبار الإصلاح</h2>
        <button onclick="testVaccinesAPI()">اختبار vaccines-api.php</button>
        <button onclick="testLoadMonthlyPlanning()">اختبار تحميل التخطيط الشهري</button>
        <button onclick="testSaveMonthlyPlanning()">اختبار حفظ التخطيط الشهري</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز للاختبار
    </div>
    
    <div class="test-section">
        <h3>📝 نتائج الاختبار</h3>
        <pre id="testResults">لم يتم تشغيل أي اختبار بعد...</pre>
    </div>

    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            testLog.push(logEntry);
            
            document.getElementById('testResults').textContent = testLog.join('\n');
            
            updateStatus(message, type);
            console.log(logEntry);
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
        }
        
        async function testVaccinesAPI() {
            log('بدء اختبار vaccines-api.php...', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load_monthly_planning',
                        user_id: 'test_fix_user'
                    })
                });
                
                log(`حالة الاستجابة: ${response.status}`, 'info');
                log(`نوع المحتوى: ${response.headers.get('content-type')}`, 'info');
                
                const responseText = await response.text();
                log(`نص الاستجابة: ${responseText.substring(0, 200)}...`, 'info');
                
                if (responseText.includes('<br />') || responseText.includes('Fatal error')) {
                    throw new Error('لا يزال هناك خطأ PHP في الكود');
                }
                
                const data = JSON.parse(responseText);
                
                if (data.success !== undefined) {
                    log('✅ vaccines-api.php يعمل بشكل صحيح - لا مزيد من أخطاء PHP', 'pass');
                    log(`البيانات المستلمة: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    log('⚠️ استجابة غير متوقعة من vaccines-api.php', 'fail');
                }
                
            } catch (error) {
                log('❌ خطأ في vaccines-api.php: ' + error.message, 'fail');
            }
        }
        
        async function testLoadMonthlyPlanning() {
            log('بدء اختبار تحميل التخطيط الشهري...', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load_monthly_planning',
                        user_id: 'test_load_user'
                    })
                });
                
                const responseText = await response.text();
                const data = JSON.parse(responseText);
                
                if (data.success && data.planning) {
                    log('✅ تحميل التخطيط الشهري يعمل بشكل صحيح', 'pass');
                    log(`تم تحميل ${Object.keys(data.planning).length} أشهر`, 'info');
                } else if (data.success && data.is_default) {
                    log('✅ تم تحميل التخطيط الافتراضي بنجاح', 'pass');
                } else {
                    log('❌ فشل في تحميل التخطيط الشهري: ' + data.message, 'fail');
                }
                
            } catch (error) {
                log('❌ خطأ في تحميل التخطيط الشهري: ' + error.message, 'fail');
            }
        }
        
        async function testSaveMonthlyPlanning() {
            log('بدء اختبار حفظ التخطيط الشهري...', 'info');
            
            const testPlanning = {
                month1: {
                    name: 'يناير 2025 - اختبار',
                    vaccines: {
                        HB1: 50,
                        BCG: 30,
                        VPO: 25
                    }
                },
                month2: {
                    name: 'فبراير 2025 - اختبار',
                    vaccines: {
                        Penta: 40,
                        RR: 35
                    }
                }
            };
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'save_monthly_planning',
                        user_id: 'test_save_user',
                        center_id: '1',
                        planning: testPlanning
                    })
                });
                
                const responseText = await response.text();
                const data = JSON.parse(responseText);
                
                if (data.success) {
                    log('✅ حفظ التخطيط الشهري يعمل بشكل صحيح', 'pass');
                    log(`تم حفظ الأشهر: ${data.saved_months?.join(', ')}`, 'info');
                    
                    // اختبار التحميل بعد الحفظ
                    setTimeout(async () => {
                        log('اختبار تحميل البيانات المحفوظة...', 'info');
                        
                        const loadResponse = await fetch('vaccines-api.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                action: 'load_monthly_planning',
                                user_id: 'test_save_user'
                            })
                        });
                        
                        const loadData = await loadResponse.json();
                        
                        if (loadData.success && loadData.planning.month1.name === 'يناير 2025 - اختبار') {
                            log('✅ تم تحميل البيانات المحفوظة بنجاح', 'pass');
                        } else {
                            log('❌ فشل في تحميل البيانات المحفوظة', 'fail');
                        }
                    }, 1000);
                    
                } else {
                    log('❌ فشل في حفظ التخطيط الشهري: ' + data.message, 'fail');
                }
                
            } catch (error) {
                log('❌ خطأ في حفظ التخطيط الشهري: ' + error.message, 'fail');
            }
        }
        
        // بدء الاختبار التلقائي
        window.onload = function() {
            log('تم تحميل صفحة التحقق من الإصلاح', 'info');
            
            setTimeout(() => {
                testVaccinesAPI();
            }, 1000);
        };
    </script>
</body>
</html>
