<?php
/**
 * Fix API Database Table Mismatches
 * إصلاح عدم تطابق جداول قاعدة البيانات في API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'success' => false,
    'timestamp' => date('Y-m-d H:i:s'),
    'fixes_applied' => [],
    'issues_found' => []
];

try {
    require_once 'config/database-live.php';
    $pdo = getDatabaseConnection();
    
    // Check what tables actually exist
    $stmt = $pdo->query("SHOW TABLES");
    $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $response['existing_tables'] = $existing_tables;
    
    // Issue 1: Medicines API expects 'medicine_list' but we have 'medicines'
    if (in_array('medicines', $existing_tables) && !in_array('medicine_list', $existing_tables)) {
        $response['issues_found'][] = 'Medicines API expects medicine_list table but only medicines exists';
        
        // Create medicine_list as a view or copy
        $pdo->exec("CREATE TABLE IF NOT EXISTS medicine_list (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50),
            center_id INT,
            name VARCHAR(255) NOT NULL,
            unit VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Copy existing medicines to medicine_list format
        $stmt = $pdo->query("SELECT COUNT(*) FROM medicine_list");
        if ($stmt->fetchColumn() == 0) {
            $pdo->exec("INSERT INTO medicine_list (id, name, unit) 
                       SELECT CONCAT('med_', id), name, COALESCE(dosage, type) FROM medicines");
        }
        
        $response['fixes_applied'][] = 'Created medicine_list table and copied data from medicines';
    }
    
    // Issue 2: Check children table structure
    if (in_array('children', $existing_tables)) {
        $stmt = $pdo->query("DESCRIBE children");
        $children_columns = array_column($stmt->fetchAll(), 'Field');
        
        // Add missing columns that APIs expect
        $expected_columns = [
            'vaccination_dates' => 'TEXT',
            'completed_vaccinations' => 'TEXT',
            'parent_name' => 'VARCHAR(255)',
            'parent_phone' => 'VARCHAR(50)',
            'address' => 'TEXT',
            'gender' => "ENUM('male', 'female')"
        ];
        
        foreach ($expected_columns as $column => $type) {
            if (!in_array($column, $children_columns)) {
                try {
                    $pdo->exec("ALTER TABLE children ADD COLUMN $column $type");
                    $response['fixes_applied'][] = "Added column $column to children table";
                } catch (Exception $e) {
                    $response['issues_found'][] = "Failed to add column $column: " . $e->getMessage();
                }
            }
        }
    }
    
    // Issue 3: Create missing API tables
    $api_tables = [
        'medicine_monthly_planning' => "CREATE TABLE IF NOT EXISTS medicine_monthly_planning (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(50),
            center_id INT,
            month_key VARCHAR(20),
            month_name VARCHAR(50),
            medicines_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_month (user_id, month_key)
        )",
        'family_planning_monthly_planning' => "CREATE TABLE IF NOT EXISTS family_planning_monthly_planning (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(50),
            center_id INT,
            month_key VARCHAR(20),
            month_name VARCHAR(50),
            contraceptives_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_month (user_id, month_key)
        )"
    ];
    
    foreach ($api_tables as $table_name => $create_sql) {
        if (!in_array($table_name, $existing_tables)) {
            $pdo->exec($create_sql);
            $response['fixes_applied'][] = "Created missing table: $table_name";
        }
    }
    
    // Issue 4: Test API endpoints
    $api_tests = [
        'medicines-api.php' => ['action' => 'test'],
        'children-api.php' => ['action' => 'load'],
        'family-planning-api.php' => ['action' => 'test'],
        'user-api-simple.php' => ['action' => 'test']
    ];
    
    $response['api_tests'] = [];
    foreach ($api_tests as $api_file => $test_data) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/$api_file");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            $result = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $response['api_tests'][$api_file] = [
                'status' => $http_code == 200 ? 'SUCCESS' : 'FAILED',
                'http_code' => $http_code,
                'response' => $result ? json_decode($result, true) : null
            ];
        } catch (Exception $e) {
            $response['api_tests'][$api_file] = [
                'status' => 'ERROR',
                'error' => $e->getMessage()
            ];
        }
    }
    
    // Final table count
    $stmt = $pdo->query("SHOW TABLES");
    $final_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $response['final_table_count'] = count($final_tables);
    $response['final_tables'] = $final_tables;
    
    $response['success'] = true;
    $response['message'] = 'API database fixes completed';
    $response['summary'] = [
        'fixes_applied' => count($response['fixes_applied']),
        'issues_found' => count($response['issues_found']),
        'tables_total' => count($final_tables)
    ];
    
} catch (Exception $e) {
    $response['error'] = $e->getMessage();
    $response['message'] = 'Error during API fixes';
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
