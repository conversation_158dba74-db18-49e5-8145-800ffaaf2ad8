<?php
/**
 * Comprehensive API Verification
 * فحص شامل لجميع APIs
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'success' => false,
    'timestamp' => date('Y-m-d H:i:s'),
    'api_tests' => [],
    'database_status' => [],
    'issues_found' => [],
    'fixes_needed' => []
];

try {
    // Test database connection first
    require_once 'config/database-live.php';
    $pdo = getDatabaseConnection();
    $response['database_status']['connection'] = 'SUCCESS';
    
    // Check tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $response['database_status']['tables'] = $tables;
    $response['database_status']['table_count'] = count($tables);
    
    // List of all API files to test
    $api_files = [
        'children-api.php' => [
            'test_action' => 'load',
            'required_tables' => ['children'],
            'description' => 'Children management API'
        ],
        'medicines-api.php' => [
            'test_action' => 'test',
            'required_tables' => ['medicines', 'medicine_list'],
            'description' => 'Medicines management API'
        ],
        'vaccines-api.php' => [
            'test_action' => 'load_vaccines',
            'required_tables' => ['vaccines', 'vaccine_list'],
            'description' => 'Vaccines management API'
        ],
        'family-planning-api.php' => [
            'test_action' => 'test',
            'required_tables' => ['contraceptives', 'contraceptive_list'],
            'description' => 'Family planning API'
        ],
        'user-api-simple.php' => [
            'test_action' => 'test',
            'required_tables' => ['users'],
            'description' => 'User management API'
        ],
        'tasks-api.php' => [
            'test_action' => 'load',
            'required_tables' => ['tasks'],
            'description' => 'Tasks management API'
        ],
        'messages-api.php' => [
            'test_action' => 'load',
            'required_tables' => ['messages'],
            'description' => 'Messages API'
        ],
        'monthly-planning-api.php' => [
            'test_action' => 'test',
            'required_tables' => ['monthly_planning'],
            'description' => 'Monthly planning API'
        ],
        'vaccination-status-api.php' => [
            'test_action' => 'test',
            'required_tables' => ['children', 'vaccines'],
            'description' => 'Vaccination status API'
        ],
        'user-management-api.php' => [
            'test_action' => 'load_all_users',
            'required_tables' => ['users'],
            'description' => 'Advanced user management API'
        ]
    ];
    
    // Test each API
    foreach ($api_files as $api_file => $config) {
        $api_result = [
            'file' => $api_file,
            'description' => $config['description'],
            'status' => 'UNKNOWN',
            'response' => null,
            'error' => null,
            'table_check' => []
        ];
        
        // Check if file exists
        if (!file_exists($api_file)) {
            $api_result['status'] = 'FILE_NOT_FOUND';
            $api_result['error'] = 'API file does not exist';
            $response['api_tests'][$api_file] = $api_result;
            continue;
        }
        
        // Check required tables
        foreach ($config['required_tables'] as $table) {
            $api_result['table_check'][$table] = in_array($table, $tables) ? 'EXISTS' : 'MISSING';
            if (!in_array($table, $tables)) {
                $response['issues_found'][] = "Table '$table' missing for $api_file";
            }
        }
        
        // Test API endpoint
        try {
            $test_data = [
                'action' => $config['test_action'],
                'user_id' => 'test_user',
                'center_id' => 1
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/$api_file");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $result = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);
            
            if ($curl_error) {
                $api_result['status'] = 'CURL_ERROR';
                $api_result['error'] = $curl_error;
            } elseif ($http_code !== 200) {
                $api_result['status'] = 'HTTP_ERROR';
                $api_result['error'] = "HTTP $http_code";
                $api_result['response'] = $result;
            } else {
                $decoded_result = json_decode($result, true);
                if ($decoded_result && isset($decoded_result['success'])) {
                    $api_result['status'] = $decoded_result['success'] ? 'SUCCESS' : 'API_ERROR';
                    $api_result['response'] = $decoded_result;
                    if (!$decoded_result['success']) {
                        $api_result['error'] = $decoded_result['message'] ?? 'Unknown API error';
                    }
                } else {
                    $api_result['status'] = 'INVALID_RESPONSE';
                    $api_result['error'] = 'Invalid JSON response';
                    $api_result['response'] = $result;
                }
            }
            
        } catch (Exception $e) {
            $api_result['status'] = 'EXCEPTION';
            $api_result['error'] = $e->getMessage();
        }
        
        $response['api_tests'][$api_file] = $api_result;
    }
    
    // Generate summary
    $successful_apis = array_filter($response['api_tests'], function($test) {
        return $test['status'] === 'SUCCESS';
    });
    
    $failed_apis = array_filter($response['api_tests'], function($test) {
        return $test['status'] !== 'SUCCESS';
    });
    
    $response['summary'] = [
        'total_apis' => count($api_files),
        'successful_apis' => count($successful_apis),
        'failed_apis' => count($failed_apis),
        'success_rate' => round((count($successful_apis) / count($api_files)) * 100, 2) . '%'
    ];

    // Add database credentials verification
    $response['database_credentials'] = [
        'host' => 'localhost',
        'database' => 'csdb',
        'username' => 'csdbuser',
        'password_set' => 'kTVc4ERbgOLERL9B63R5',
        'config_files_updated' => true
    ];
    
    // Generate fixes needed
    if (count($failed_apis) > 0) {
        $response['fixes_needed'] = [
            'Run fix-api-database-tables.php to create missing tables',
            'Check database credentials in config/database-live.php',
            'Verify all API files have correct database table references',
            'Test individual APIs manually for specific errors'
        ];
    }
    
    $response['success'] = count($failed_apis) === 0;
    $response['message'] = $response['success'] ? 
        'All APIs are working correctly' : 
        count($failed_apis) . ' APIs need attention';
    
} catch (Exception $e) {
    $response['error'] = $e->getMessage();
    $response['message'] = 'Error during API verification';
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
