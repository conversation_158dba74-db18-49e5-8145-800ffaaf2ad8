<?php
/**
 * Final System Verification - Complete Check
 * التحقق النهائي من النظام - فحص شامل
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'verification_type' => 'FINAL_COMPLETE_CHECK',
    'database_verification' => [],
    'api_verification' => [],
    'configuration_verification' => [],
    'file_verification' => [],
    'security_verification' => [],
    'overall_status' => 'UNKNOWN',
    'recommendations' => []
];

try {
    // ===========================================
    // 1. DATABASE VERIFICATION
    // ===========================================
    
    $response['database_verification']['credentials'] = [
        'expected_host' => 'localhost',
        'expected_database' => 'csdb',
        'expected_username' => 'csdbuser',
        'expected_password' => 'kTVc4ERbgOLERL9B63R5'
    ];
    
    // Test database connection
    try {
        require_once 'config/database-live.php';
        $pdo = getDatabaseConnection();
        
        $stmt = $pdo->query("SELECT DATABASE() as db_name, USER() as current_user, VERSION() as mysql_version");
        $db_info = $stmt->fetch();
        
        $response['database_verification']['connection'] = [
            'status' => 'SUCCESS',
            'current_database' => $db_info['db_name'],
            'current_user' => $db_info['current_user'],
            'mysql_version' => $db_info['mysql_version']
        ];
        
        // Verify correct database
        if ($db_info['db_name'] !== 'csdb') {
            $response['database_verification']['connection']['warning'] = 'Connected to wrong database: ' . $db_info['db_name'];
        }
        
        // Check all required tables
        $required_tables = [
            'users', 'centers', 'children', 'medicines', 'vaccines', 'contraceptives',
            'tasks', 'messages', 'notifications', 'monthly_planning',
            'medicine_list', 'vaccine_list', 'contraceptive_list',
            'medicine_monthly_planning', 'vaccine_monthly_planning', 'family_planning_monthly_planning'
        ];
        
        $stmt = $pdo->query("SHOW TABLES");
        $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $missing_tables = array_diff($required_tables, $existing_tables);
        $extra_tables = array_diff($existing_tables, $required_tables);
        
        $response['database_verification']['tables'] = [
            'required_count' => count($required_tables),
            'existing_count' => count($existing_tables),
            'missing_tables' => $missing_tables,
            'extra_tables' => $extra_tables,
            'all_required_exist' => empty($missing_tables)
        ];
        
        // Check table data
        $table_data = [];
        foreach (['users', 'children', 'medicines', 'vaccines', 'contraceptives'] as $table) {
            if (in_array($table, $existing_tables)) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetch()['count'];
                $table_data[$table] = $count;
            }
        }
        $response['database_verification']['table_data'] = $table_data;
        
    } catch (Exception $e) {
        $response['database_verification']['connection'] = [
            'status' => 'FAILED',
            'error' => $e->getMessage()
        ];
    }
    
    // ===========================================
    // 2. API VERIFICATION
    // ===========================================
    
    $critical_apis = [
        'user-api-simple.php' => 'test',
        'children-api.php' => 'load',
        'medicines-api.php' => 'test',
        'vaccines-api.php' => 'load_vaccines',
        'family-planning-api.php' => 'test'
    ];
    
    $api_results = [];
    $working_apis = 0;
    
    foreach ($critical_apis as $api_file => $action) {
        $start_time = microtime(true);
        
        try {
            $test_data = ['action' => $action, 'user_id' => 'test_user'];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/$api_file");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $result = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $response_time = round((microtime(true) - $start_time) * 1000, 2);
            
            $api_response = json_decode($result, true);
            $is_working = ($http_code == 200 && $api_response && $api_response['success']);
            
            if ($is_working) {
                $working_apis++;
            }
            
            $api_results[$api_file] = [
                'status' => $is_working ? 'SUCCESS' : 'FAILED',
                'response_time' => $response_time,
                'http_code' => $http_code,
                'api_response' => $api_response
            ];
            
        } catch (Exception $e) {
            $api_results[$api_file] = [
                'status' => 'ERROR',
                'error' => $e->getMessage(),
                'response_time' => 0
            ];
        }
    }
    
    $response['api_verification'] = [
        'total_tested' => count($critical_apis),
        'working_count' => $working_apis,
        'success_rate' => round(($working_apis / count($critical_apis)) * 100, 1) . '%',
        'results' => $api_results
    ];
    
    // ===========================================
    // 3. CONFIGURATION FILE VERIFICATION
    // ===========================================
    
    $config_files = [
        'config/database-live.php' => 'Primary database config',
        'config/database.php' => 'Main database class',
        'config/database-new.php' => 'Alternative database config'
    ];
    
    $config_status = [];
    foreach ($config_files as $file => $description) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $has_correct_password = strpos($content, 'kTVc4ERbgOLERL9B63R5') !== false;
            $has_old_password = strpos($content, 'j5aKN6lz5bsujTcWaYAd') !== false;
            
            $config_status[$file] = [
                'exists' => true,
                'description' => $description,
                'has_correct_password' => $has_correct_password,
                'has_old_password' => $has_old_password,
                'status' => $has_correct_password && !$has_old_password ? 'CORRECT' : 'NEEDS_UPDATE'
            ];
        } else {
            $config_status[$file] = [
                'exists' => false,
                'description' => $description,
                'status' => 'MISSING'
            ];
        }
    }
    
    $response['configuration_verification'] = $config_status;
    
    // ===========================================
    // 4. FILE VERIFICATION
    // ===========================================
    
    $critical_files = [
        'index.html' => 'Main website interface',
        'children-api.php' => 'Children management API',
        'medicines-api.php' => 'Medicine management API',
        'vaccines-api.php' => 'Vaccine management API',
        'family-planning-api.php' => 'Family planning API',
        'user-api-simple.php' => 'User management API'
    ];
    
    $file_status = [];
    foreach ($critical_files as $file => $description) {
        $file_status[$file] = [
            'exists' => file_exists($file),
            'description' => $description,
            'size' => file_exists($file) ? filesize($file) : 0,
            'readable' => file_exists($file) ? is_readable($file) : false
        ];
    }
    
    $response['file_verification'] = $file_status;
    
    // ===========================================
    // 5. OVERALL ASSESSMENT
    // ===========================================
    
    $issues = [];
    $warnings = [];
    
    // Database issues
    if ($response['database_verification']['connection']['status'] !== 'SUCCESS') {
        $issues[] = 'Database connection failed';
    }
    
    if (!empty($response['database_verification']['tables']['missing_tables'])) {
        $issues[] = 'Missing required database tables: ' . implode(', ', $response['database_verification']['tables']['missing_tables']);
    }
    
    // API issues
    if ($working_apis < count($critical_apis)) {
        $failed_apis = array_keys(array_filter($api_results, function($result) {
            return $result['status'] !== 'SUCCESS';
        }));
        $issues[] = 'Failed APIs: ' . implode(', ', $failed_apis);
    }
    
    // Configuration issues
    $config_issues = array_filter($config_status, function($config) {
        return $config['status'] !== 'CORRECT';
    });
    
    if (!empty($config_issues)) {
        $warnings[] = 'Configuration files need updates: ' . implode(', ', array_keys($config_issues));
    }
    
    // File issues
    $missing_files = array_filter($file_status, function($file) {
        return !$file['exists'];
    });
    
    if (!empty($missing_files)) {
        $issues[] = 'Missing critical files: ' . implode(', ', array_keys($missing_files));
    }
    
    // Determine overall status
    if (empty($issues)) {
        if (empty($warnings)) {
            $response['overall_status'] = 'EXCELLENT';
            $response['message'] = 'System is fully configured and ready for production use';
        } else {
            $response['overall_status'] = 'GOOD';
            $response['message'] = 'System is working well with minor configuration warnings';
        }
    } else {
        $response['overall_status'] = 'NEEDS_ATTENTION';
        $response['message'] = 'System has issues that need to be resolved';
    }
    
    // Generate recommendations
    if (!empty($issues)) {
        $response['recommendations'] = array_merge($response['recommendations'], $issues);
    }
    
    if (!empty($warnings)) {
        $response['recommendations'] = array_merge($response['recommendations'], $warnings);
    }
    
    if (empty($response['recommendations'])) {
        $response['recommendations'] = [
            'System is fully configured and ready for use!',
            'All database connections are working correctly',
            'All critical APIs are functional',
            'Configuration files are properly set up'
        ];
    }
    
    $response['next_steps'] = [
        'Visit https://www.csmanager.online/ to use the system',
        'Login with: admin / password',
        'Test all functionality to ensure everything works as expected',
        'Create additional user accounts as needed'
    ];
    
} catch (Exception $e) {
    $response['overall_status'] = 'SYSTEM_ERROR';
    $response['message'] = 'Verification failed: ' . $e->getMessage();
    $response['error'] = $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
