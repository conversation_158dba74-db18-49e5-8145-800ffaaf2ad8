<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شريط التقدم في صفحة سجل الأطفال الكامل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .children-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .child-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 10px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .child-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .child-name {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .child-birth-date {
            color: #666;
            margin-bottom: 15px;
        }
        
        .vaccination-progress-container {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .progress-count {
            font-weight: bold;
            color: #007bff;
        }
        
        .progress-bar-container {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .progress-bar {
            background: #28a745;
            height: 100%;
            transition: width 0.5s ease-in-out;
            border-radius: 4px;
        }
        
        .progress-percentage {
            text-align: center;
            font-weight: bold;
            color: #28a745;
            font-size: 0.9em;
        }
        
        .child-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .btn-view {
            background: #007bff;
            color: white;
        }
        
        .btn-view:hover {
            background: #0056b3;
        }
        
        .btn-delete-child {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete-child:hover {
            background: #c82333;
        }
        
        .btn-update {
            background: #28a745;
            color: white;
        }
        
        .btn-update:hover {
            background: #1e7e34;
        }
        
        .test-controls {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار شريط التقدم في صفحة سجل الأطفال الكامل</h1>
        
        <div class="test-controls">
            <h3>🎮 أدوات التحكم في الاختبار</h3>
            <button class="btn btn-update" onclick="simulateVaccinationUpdate()">✅ محاكاة إكمال تلقيح</button>
            <button class="btn btn-update" onclick="simulateVaccinationCancel()">❌ محاكاة إلغاء تلقيح</button>
            <button class="btn btn-view" onclick="updateAllProgressBarsTest()">🔄 تحديث جميع الأشرطة</button>
            <button class="btn btn-view" onclick="resetAllProgress()">🔄 إعادة تعيين الكل</button>
        </div>

        <div id="testResults"></div>

        <div class="children-grid" id="childrenGrid">
            <!-- سيتم ملء هذا القسم بـ JavaScript -->
        </div>
    </div>

    <script>
        // بيانات تجريبية للأطفال
        let testChildren = [
            {
                id: 'child-1',
                name: 'أحمد محمد',
                birthDate: '2023-01-15',
                completedVaccinations: { 0: true, 1: true },
                vaccinationDates: new Array(12).fill(null).map((_, i) => ({ index: i, name: `تلقيح ${i + 1}` }))
            },
            {
                id: 'child-2',
                name: 'فاطمة علي',
                birthDate: '2023-03-20',
                completedVaccinations: { 0: true, 1: true, 2: true, 3: true },
                vaccinationDates: new Array(12).fill(null).map((_, i) => ({ index: i, name: `تلقيح ${i + 1}` }))
            },
            {
                id: 'child-3',
                name: 'عمر حسن',
                birthDate: '2023-05-10',
                completedVaccinations: { 0: true },
                vaccinationDates: new Array(12).fill(null).map((_, i) => ({ index: i, name: `تلقيح ${i + 1}` }))
            },
            {
                id: 'child-4',
                name: 'زينب أحمد',
                birthDate: '2023-07-05',
                completedVaccinations: {},
                vaccinationDates: new Array(12).fill(null).map((_, i) => ({ index: i, name: `تلقيح ${i + 1}` }))
            }
        ];

        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `test-result ${type}`;
            resultElement.innerHTML = `${new Date().toLocaleTimeString('ar-SA')} - ${message}`;
            resultsDiv.appendChild(resultElement);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function createChildCard(child) {
            const card = document.createElement('div');
            card.className = 'child-card child-grid-card';
            card.dataset.childId = child.id;

            const completedVaccinations = child.completedVaccinations || {};
            const completedCount = Object.values(completedVaccinations).filter(status => status === true).length;
            const totalCount = child.vaccinationDates ? child.vaccinationDates.length : 12;
            const completionPercentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

            card.innerHTML = `
                <h3 class="child-name">${child.name}</h3>
                <p class="child-birth-date">📅 ${child.birthDate}</p>
                <div class="vaccination-progress-container">
                    <div class="progress-info">
                        <span>التلقيحات المنجزة:</span>
                        <span class="progress-count">${completedCount}/${totalCount}</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: ${completionPercentage}%"></div>
                    </div>
                    <div class="progress-percentage">${completionPercentage}%</div>
                </div>
                <div class="child-actions">
                    <button onclick="loadChildData('${child.id}')" class="btn btn-view">📋 عرض</button>
                    <button onclick="addVaccination('${child.id}')" class="btn btn-update">➕ إضافة تلقيح</button>
                </div>
            `;

            return card;
        }

        function updateProgressBarForChild(childId) {
            const child = testChildren.find(c => c.id === childId);
            if (!child) return;

            const completedVaccinations = child.completedVaccinations || {};
            const completedCount = Object.values(completedVaccinations).filter(status => status === true).length;
            const totalCount = child.vaccinationDates ? child.vaccinationDates.length : 12;
            const progressPercent = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

            const card = document.querySelector(`[data-child-id="${childId}"]`);
            if (card) {
                const progressBar = card.querySelector('.progress-bar');
                const progressInfo = card.querySelector('.progress-count');
                const progressPercentage = card.querySelector('.progress-percentage');
                
                if (progressBar) {
                    progressBar.style.width = `${progressPercent}%`;
                    progressBar.style.transition = 'width 0.5s ease-in-out';
                }
                if (progressInfo) {
                    progressInfo.textContent = `${completedCount}/${totalCount}`;
                }
                if (progressPercentage) {
                    progressPercentage.textContent = `${progressPercent}%`;
                }
            }

            logResult(`✅ تم تحديث شريط التقدم للطفل ${child.name}: ${progressPercent}% (${completedCount}/${totalCount})`, 'success');
        }

        function displayChildrenGrid() {
            const grid = document.getElementById('childrenGrid');
            grid.innerHTML = '';

            testChildren.forEach(child => {
                const childCard = createChildCard(child);
                grid.appendChild(childCard);
            });

            logResult('🔄 تم عرض شبكة الأطفال', 'info');
        }

        function simulateVaccinationUpdate() {
            const randomChild = testChildren[Math.floor(Math.random() * testChildren.length)];
            const completedCount = Object.values(randomChild.completedVaccinations).filter(status => status === true).length;
            
            if (completedCount < 12) {
                randomChild.completedVaccinations[completedCount] = true;
                updateProgressBarForChild(randomChild.id);
                logResult(`✅ تم إكمال تلقيح جديد للطفل ${randomChild.name}`, 'success');
            } else {
                logResult(`ℹ️ جميع تلقيحات الطفل ${randomChild.name} مكتملة`, 'info');
            }
        }

        function simulateVaccinationCancel() {
            const randomChild = testChildren[Math.floor(Math.random() * testChildren.length)];
            const completedKeys = Object.keys(randomChild.completedVaccinations).filter(key => randomChild.completedVaccinations[key] === true);
            
            if (completedKeys.length > 0) {
                const lastKey = completedKeys[completedKeys.length - 1];
                delete randomChild.completedVaccinations[lastKey];
                updateProgressBarForChild(randomChild.id);
                logResult(`❌ تم إلغاء تلقيح للطفل ${randomChild.name}`, 'success');
            } else {
                logResult(`ℹ️ لا توجد تلقيحات لإلغائها للطفل ${randomChild.name}`, 'info');
            }
        }

        function updateAllProgressBarsTest() {
            testChildren.forEach(child => {
                updateProgressBarForChild(child.id);
            });
            logResult('🔄 تم تحديث جميع أشرطة التقدم', 'success');
        }

        function resetAllProgress() {
            testChildren.forEach(child => {
                child.completedVaccinations = {};
                updateProgressBarForChild(child.id);
            });
            logResult('🔄 تم إعادة تعيين جميع أشرطة التقدم', 'info');
        }

        function addVaccination(childId) {
            const child = testChildren.find(c => c.id === childId);
            if (child) {
                const completedCount = Object.values(child.completedVaccinations).filter(status => status === true).length;
                if (completedCount < 12) {
                    child.completedVaccinations[completedCount] = true;
                    updateProgressBarForChild(childId);
                }
            }
        }

        function loadChildData(childId) {
            const child = testChildren.find(c => c.id === childId);
            if (child) {
                logResult(`📋 تم تحميل بيانات الطفل: ${child.name}`, 'info');
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            logResult('🚀 بدء اختبار شريط التقدم في صفحة سجل الأطفال الكامل', 'info');
            displayChildrenGrid();
            logResult('✅ تم تحميل الصفحة بنجاح', 'success');
        });
    </script>
</body>
</html>
