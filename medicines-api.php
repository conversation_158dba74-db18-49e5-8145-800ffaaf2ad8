<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// تضمين إعدادات قاعدة البيانات
require_once 'config/database-live.php';

try {
    // الاتصال بقاعدة البيانات باستخدام الإعدادات المناسبة
    $pdo = getDatabaseConnection();

    // قراءة البيانات المرسلة
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('خطأ في تحليل JSON: ' . json_last_error_msg());
    }

    if (!$input) {
        $input = $_POST;
    }

    $action = $input['action'] ?? $_GET['action'] ?? '';

    if (empty($action)) {
        throw new Exception('لم يتم تحديد الإجراء المطلوب');
    }
    
    // إنشاء الجداول عند الحاجة (مع معالجة الأخطاء)
    try {
        createMedicineTables($pdo);
    } catch (Exception $e) {
        error_log("Warning: Could not create medicine tables: " . $e->getMessage());
        // المتابعة حتى لو فشل إنشاء الجداول
    }
    
    switch ($action) {
        case 'test':
            // اختبار اتصال قاعدة البيانات
            try {
                $stmt = $pdo->query("SELECT 1 as test");
                $result = $stmt->fetch();

                echo json_encode([
                    'success' => true,
                    'message' => 'Medicines API يعمل بشكل صحيح',
                    'database' => 'متصل',
                    'test_query' => $result ? 'نجح' : 'فشل',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'user_id' => $input['user_id'] ?? 'غير محدد'
                ], JSON_UNESCAPED_UNICODE);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Medicines API يعمل لكن قاعدة البيانات بها مشكلة',
                    'database_error' => $e->getMessage(),
                    'timestamp' => date('Y-m-d H:i:s')
                ], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        case 'load':
        case 'load_medicine_list':
            loadMedicineListAPI($pdo, $input);
            break;
            
        case 'add_medicine':
            addMedicineAPI($pdo, $input);
            break;
            
        case 'delete_medicine':
            deleteMedicineAPI($pdo, $input);
            break;
            
        case 'save_medicine_monthly_planning':
            saveMedicineMonthlyPlanningAPI($pdo, $input);
            break;
            
        case 'load_medicine_monthly_planning':
            loadMedicineMonthlyPlanningAPI($pdo, $input);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح: ' . $action);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    error_log('خطأ في قاعدة البيانات medicines-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error_type' => 'database',
        'file' => 'medicines-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    error_log('خطأ عام في medicines-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_type' => 'general',
        'file' => 'medicines-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

// ===============================
// دوال إدارة الأدوية
// ===============================

// إنشاء جداول الأدوية
function createMedicineTables($pdo) {
    try {
        // جدول قائمة الأدوية
        $sql1 = "
        CREATE TABLE IF NOT EXISTS medicine_list (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            name VARCHAR(255) NOT NULL,
            unit VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        // جدول التخطيط الشهري للأدوية
        $sql2 = "
        CREATE TABLE IF NOT EXISTS medicine_monthly_planning (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            month_key VARCHAR(20) NOT NULL,
            month_name VARCHAR(100),
            medicines_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_month (user_id, month_key),
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        // جدول مخزون الأدوية
        $sql3 = "
        CREATE TABLE IF NOT EXISTS medicine_stock (
            id INT AUTO_INCREMENT PRIMARY KEY,
            medicine_id VARCHAR(50),
            user_id VARCHAR(50),
            center_id VARCHAR(50),
            quantity INT DEFAULT 0,
            expiry_date DATE,
            batch_number VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_medicine_user (medicine_id, user_id),
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        $pdo->exec($sql1);
        $pdo->exec($sql2);
        $pdo->exec($sql3);
        
    } catch (Exception $e) {
        error_log('خطأ في إنشاء جداول الأدوية: ' . $e->getMessage());
    }
}

// تحميل قائمة الأدوية
function loadMedicineListAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    
    if (!$user_id) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    try {
        // Try medicine_list first, fallback to medicines table
        $medicines = [];
        try {
            $stmt = $pdo->prepare("SELECT id, name, unit FROM medicine_list WHERE user_id = ? ORDER BY name");
            $stmt->execute([$user_id]);
            $medicines = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // Fallback to medicines table
            try {
                $stmt = $pdo->prepare("SELECT id, name, COALESCE(dosage, type) as unit, stock FROM medicines ORDER BY name");
                $stmt->execute();
                $medicines_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Convert to expected format
                foreach ($medicines_data as $med) {
                    $medicines[] = [
                        'id' => 'med_' . $med['id'],
                        'name' => $med['name'],
                        'unit' => $med['unit'],
                        'stock' => $med['stock'] ?? 0
                    ];
                }
            } catch (Exception $e2) {
                // If both fail, return empty list
                echo json_encode([
                    'success' => true,
                    'medicines' => [],
                    'count' => 0,
                    'message' => 'المخزون فارغ - لم يتم إعداد جداول الأدوية بعد'
                ], JSON_UNESCAPED_UNICODE);
                return;
            }
        }
        $medicineList = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب التخطيط الشهري للأشهر الثلاثة
        $stmt = $pdo->prepare("SELECT medicines_data FROM medicine_monthly_planning WHERE user_id = ? ORDER BY month_key");
        $stmt->execute([$user_id]);
        $planningResults = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // تجميع الكميات من التخطيط الشهري
        $medicineQuantities = [];
        foreach ($planningResults as $planning) {
            $medicinesData = json_decode($planning['medicines_data'], true);
            if ($medicinesData && is_array($medicinesData)) {
                // البيانات محفوظة بتنسيق {id: quantity}
                foreach ($medicinesData as $medicineId => $quantity) {
                    $quantity = intval($quantity);

                    if ($medicineId && $quantity > 0) {
                        if (!isset($medicineQuantities[$medicineId])) {
                            $medicineQuantities[$medicineId] = 0;
                        }
                        $medicineQuantities[$medicineId] += $quantity;
                    }
                }
            }
        }

        // دمج قائمة الأدوية مع الكميات من التخطيط
        $medicines = [];
        foreach ($medicineList as $medicine) {
            $medicines[] = [
                'id' => $medicine['id'],
                'name' => $medicine['name'],
                'unit' => $medicine['unit'],
                'quantity' => $medicineQuantities[$medicine['id']] ?? 0
            ];
        }

        // تسجيل للتشخيص
        error_log("Medicines query executed for user: $user_id, found: " . count($medicines) . " medicines from planning data");
        
        // إذا لم توجد أدوية، إرجاع قائمة فارغة مع رسالة
        if (empty($medicines)) {
            echo json_encode([
                'success' => true,
                'medicines' => [],
                'count' => 0,
                'message' => 'لا توجد أدوية مسجلة لهذا المستخدم'
            ], JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode([
                'success' => true,
                'medicines' => $medicines,
                'count' => count($medicines),
                'message' => 'تم جلب قائمة الأدوية بنجاح'
            ], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (Exception $e) {
        throw $e;
    }
}

// إضافة دواء جديد
function addMedicineAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '1';
    $name = trim($input['name'] ?? '');
    $unit = trim($input['unit'] ?? '');
    
    if (!$user_id || !$name || !$unit) {
        throw new Exception('جميع البيانات مطلوبة: معرف المستخدم، اسم الدواء، الوحدة');
    }
    
    try {
        // إنشاء معرف فريد للدواء
        $medicine_id = time() . rand(1000, 9999);
        
        // Try medicine_list first, fallback to medicines table
        try {
            $stmt = $pdo->prepare("
                INSERT INTO medicine_list (id, user_id, center_id, name, unit)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$medicine_id, $user_id, $center_id, $name, $unit]);
        } catch (Exception $e) {
            // Fallback to medicines table
            $stmt = $pdo->prepare("
                INSERT INTO medicines (name, type, dosage, stock)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$name, 'دواء', $unit, 0]);
            $medicine_id = $pdo->lastInsertId();
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة الدواء بنجاح',
            'medicine' => [
                'id' => $medicine_id,
                'name' => $name,
                'unit' => $unit
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        throw $e;
    }
}

// حفظ التخطيط الشهري للأدوية
function saveMedicineMonthlyPlanningAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '1';
    $planning = $input['planning'] ?? [];
    
    if (!$user_id || !is_array($planning)) {
        throw new Exception('معرف المستخدم وبيانات التخطيط مطلوبان');
    }
    
    try {
        $pdo->beginTransaction();
        
        $saved_months = [];
        
        foreach ($planning as $month_key => $month_data) {
            $month_name = $month_data['name'] ?? '';
            $medicines_data = json_encode($month_data['medicines'] ?? [], JSON_UNESCAPED_UNICODE);
            
            $stmt = $pdo->prepare("
                INSERT INTO medicine_monthly_planning (user_id, center_id, month_key, month_name, medicines_data) 
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                month_name = VALUES(month_name),
                medicines_data = VALUES(medicines_data),
                updated_at = CURRENT_TIMESTAMP
            ");
            
            $result = $stmt->execute([$user_id, $center_id, $month_key, $month_name, $medicines_data]);
            
            if ($result) {
                $saved_months[] = $month_key;
            }
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ التخطيط الشهري للأدوية بنجاح',
            'saved_months' => $saved_months,
            'user_id' => $user_id
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

// تحميل التخطيط الشهري للأدوية
function loadMedicineMonthlyPlanningAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    
    if (!$user_id) {
        echo json_encode([
            'success' => true,
            'planning' => [],
            'is_default' => true
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("SELECT month_key, month_name, medicines_data FROM medicine_monthly_planning WHERE user_id = ? ORDER BY month_key");
        $stmt->execute([$user_id]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($results)) {
            echo json_encode([
                'success' => true,
                'planning' => [],
                'is_default' => true
            ], JSON_UNESCAPED_UNICODE);
            return;
        }
        
        $planning = [];
        foreach ($results as $row) {
            $medicines_data = json_decode($row['medicines_data'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $medicines_data = [];
            }
            
            $planning[$row['month_key']] = [
                'name' => $row['month_name'] ?? '',
                'medicines' => $medicines_data
            ];
        }
        
        echo json_encode([
            'success' => true,
            'planning' => $planning,
            'loaded_months' => array_keys($planning)
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        throw $e;
    }
}

// حذف دواء
function deleteMedicineAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $medicine_id = $input['medicine_id'] ?? '';
    
    if (!$user_id || !$medicine_id) {
        throw new Exception('معرف المستخدم ومعرف الدواء مطلوبان');
    }
    
    try {
        $pdo->beginTransaction();
        
        // حذف الدواء من القائمة
        $stmt = $pdo->prepare("DELETE FROM medicine_list WHERE id = ? AND user_id = ?");
        $stmt->execute([$medicine_id, $user_id]);
        
        // حذف الدواء من المخزون
        $stmt = $pdo->prepare("DELETE FROM medicine_stock WHERE medicine_id = ? AND user_id = ?");
        $stmt->execute([$medicine_id, $user_id]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الدواء بنجاح',
            'medicine_id' => $medicine_id
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

?>
