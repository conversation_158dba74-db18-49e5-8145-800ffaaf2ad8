<?php
/**
 * فحص syntax لملف vaccines-api.php
 */

header('Content-Type: application/json; charset=utf-8');

try {
    // فحص syntax
    $output = [];
    $return_var = 0;
    
    exec('php -l vaccines-api.php 2>&1', $output, $return_var);
    
    $syntaxCheck = implode("\n", $output);
    
    if ($return_var === 0 && strpos($syntaxCheck, 'No syntax errors') !== false) {
        echo json_encode([
            'success' => true,
            'message' => 'لا توجد أخطاء syntax في vaccines-api.php',
            'syntax_check' => $syntaxCheck,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'توجد أخطاء syntax في vaccines-api.php',
            'syntax_errors' => $syntaxCheck,
            'return_code' => $return_var,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في فحص syntax: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
