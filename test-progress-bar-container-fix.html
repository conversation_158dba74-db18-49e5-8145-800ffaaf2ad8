<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح progress-bar-container</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        
        /* CSS المحدث لشريط التقدم */
        .progress-bar-container {
            background: #e9ecef !important;
            height: 10px !important;
            border-radius: 5px !important;
            overflow: hidden !important;
            border: 1px solid #dee2e6 !important;
            margin: 8px 0 !important;
            width: 100% !important;
            display: block !important;
            position: relative !important;
        }

        .progress-bar {
            background: linear-gradient(90deg, #28a745, #20c997) !important;
            height: 100% !important;
            transition: width 0.5s ease-in-out !important;
            border-radius: 4px !important;
            position: relative !important;
            min-width: 0 !important;
            display: block !important;
        }
        
        .vaccination-progress-container {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .progress-count {
            font-weight: bold;
            color: #007bff;
        }
        
        .progress-percentage {
            text-align: center;
            margin-top: 8px;
            font-size: 0.9rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .child-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح progress-bar-container</h1>
        
        <div class="test-section">
            <h3>📋 معلومات المشكلة</h3>
            <p>هذا الاختبار يتحقق من أن عنصر <code>.progress-bar-container</code> يعمل بشكل صحيح ويعرض شريط التقدم.</p>
        </div>

        <div class="test-section">
            <h3>🎯 اختبار شريط التقدم الأساسي</h3>
            <div class="child-card" data-child-id="test-1">
                <h4>اختبار أساسي - شريط التقدم العادي</h4>
                <div class="vaccination-progress-container">
                    <div class="progress-info">
                        <span>التلقيحات المنجزة:</span>
                        <span class="progress-count">3/12</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: 25%"></div>
                    </div>
                    <div class="progress-percentage">25%</div>
                </div>
                <button class="btn btn-success" onclick="updateProgress('test-1', 50)">تحديث إلى 50%</button>
                <button class="btn btn-danger" onclick="updateProgress('test-1', 0)">إعادة تعيين</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 اختبار الإصلاح التلقائي</h3>
            <div class="child-card" data-child-id="test-2">
                <h4>اختبار الإصلاح - شريط تقدم مكسور</h4>
                <div class="vaccination-progress-container">
                    <div class="progress-info">
                        <span>التلقيحات المنجزة:</span>
                        <span class="progress-count">0/12</span>
                    </div>
                    <!-- شريط التقدم مفقود عمداً -->
                    <div class="progress-percentage">0%</div>
                </div>
                <button class="btn btn-success" onclick="fixAndUpdate('test-2', 75)">إصلاح وتحديث إلى 75%</button>
                <button class="btn" onclick="debugProgressBar('test-2')">فحص العناصر</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>🔍 معلومات التشخيص</h3>
            <div id="debugInfo"></div>
        </div>
    </div>

    <script>
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `test-result ${type}`;
            resultElement.innerHTML = `${new Date().toLocaleTimeString('ar-SA')} - ${message}`;
            resultsDiv.appendChild(resultElement);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function logDebug(message) {
            const debugDiv = document.getElementById('debugInfo');
            const debugElement = document.createElement('div');
            debugElement.className = 'debug-info';
            debugElement.innerHTML = message;
            debugDiv.appendChild(debugElement);
        }

        function fixProgressBar(card, progressPercent) {
            let progressContainer = card.querySelector('.progress-bar-container');
            
            if (!progressContainer) {
                logResult('⚠️ شريط التقدم مفقود، جاري إنشاؤه...', 'error');
                
                const vaccinationContainer = card.querySelector('.vaccination-progress-container');
                if (vaccinationContainer) {
                    const progressInfo = vaccinationContainer.querySelector('.progress-info');
                    if (progressInfo) {
                        progressContainer = document.createElement('div');
                        progressContainer.className = 'progress-bar-container';
                        progressContainer.style.cssText = `
                            background: #e9ecef !important;
                            height: 10px !important;
                            border-radius: 5px !important;
                            overflow: hidden !important;
                            border: 1px solid #dee2e6 !important;
                            margin: 8px 0 !important;
                            width: 100% !important;
                            display: block !important;
                        `;
                        
                        const progressBar = document.createElement('div');
                        progressBar.className = 'progress-bar';
                        progressBar.style.cssText = `
                            background: linear-gradient(90deg, #28a745, #20c997) !important;
                            height: 100% !important;
                            transition: width 0.5s ease-in-out !important;
                            border-radius: 4px !important;
                            width: ${progressPercent}% !important;
                            display: block !important;
                        `;
                        
                        progressContainer.appendChild(progressBar);
                        progressInfo.parentNode.insertBefore(progressContainer, progressInfo.nextSibling);
                        
                        logResult('✅ تم إنشاء شريط التقدم بنجاح', 'success');
                    }
                }
            } else {
                logResult('ℹ️ شريط التقدم موجود، جاري التحديث...', 'info');
                
                let progressBar = progressContainer.querySelector('.progress-bar');
                if (!progressBar) {
                    progressBar = document.createElement('div');
                    progressBar.className = 'progress-bar';
                    progressContainer.appendChild(progressBar);
                }
                
                // تطبيق الأنماط المطلوبة
                progressContainer.style.cssText = `
                    background: #e9ecef !important;
                    height: 10px !important;
                    border-radius: 5px !important;
                    overflow: hidden !important;
                    border: 1px solid #dee2e6 !important;
                    margin: 8px 0 !important;
                    width: 100% !important;
                    display: block !important;
                `;
                
                progressBar.style.cssText = `
                    background: linear-gradient(90deg, #28a745, #20c997) !important;
                    height: 100% !important;
                    transition: width 0.5s ease-in-out !important;
                    border-radius: 4px !important;
                    width: ${progressPercent}% !important;
                    display: block !important;
                `;
                
                logResult('✅ تم تحديث شريط التقدم بنجاح', 'success');
            }
        }

        function updateProgress(cardId, progressPercent) {
            const card = document.querySelector(`[data-child-id="${cardId}"]`);
            if (!card) {
                logResult('❌ لم يتم العثور على البطاقة', 'error');
                return;
            }

            const progressBar = card.querySelector('.progress-bar');
            const progressPercentage = card.querySelector('.progress-percentage');
            
            if (progressBar) {
                progressBar.style.width = `${progressPercent}%`;
                logResult(`✅ تم تحديث شريط التقدم إلى ${progressPercent}%`, 'success');
            } else {
                logResult('❌ لم يتم العثور على عنصر شريط التقدم', 'error');
            }
            
            if (progressPercentage) {
                progressPercentage.textContent = `${progressPercent}%`;
            }
        }

        function fixAndUpdate(cardId, progressPercent) {
            const card = document.querySelector(`[data-child-id="${cardId}"]`);
            if (!card) {
                logResult('❌ لم يتم العثور على البطاقة', 'error');
                return;
            }

            // إصلاح شريط التقدم أولاً
            fixProgressBar(card, progressPercent);
            
            // تحديث النسبة المئوية
            const progressPercentage = card.querySelector('.progress-percentage');
            if (progressPercentage) {
                progressPercentage.textContent = `${progressPercent}%`;
            }
            
            logResult(`🔧 تم إصلاح وتحديث شريط التقدم إلى ${progressPercent}%`, 'success');
        }

        function debugProgressBar(cardId) {
            const card = document.querySelector(`[data-child-id="${cardId}"]`);
            if (!card) {
                logDebug('❌ لم يتم العثور على البطاقة');
                return;
            }

            const progressContainer = card.querySelector('.progress-bar-container');
            const progressBar = card.querySelector('.progress-bar');
            const vaccinationContainer = card.querySelector('.vaccination-progress-container');

            let debugInfo = `<strong>تشخيص البطاقة ${cardId}:</strong><br>`;
            debugInfo += `- البطاقة موجودة: ✅<br>`;
            debugInfo += `- vaccination-progress-container: ${vaccinationContainer ? '✅' : '❌'}<br>`;
            debugInfo += `- progress-bar-container: ${progressContainer ? '✅' : '❌'}<br>`;
            debugInfo += `- progress-bar: ${progressBar ? '✅' : '❌'}<br>`;
            
            if (progressContainer) {
                debugInfo += `- عرض الحاوية: ${progressContainer.style.display || 'default'}<br>`;
                debugInfo += `- ارتفاع الحاوية: ${progressContainer.style.height || 'default'}<br>`;
            }
            
            if (progressBar) {
                debugInfo += `- عرض الشريط: ${progressBar.style.width || 'default'}<br>`;
                debugInfo += `- خلفية الشريط: ${progressBar.style.background || 'default'}<br>`;
            }

            logDebug(debugInfo);
        }

        // تهيئة الاختبار
        document.addEventListener('DOMContentLoaded', function() {
            logResult('🚀 بدء اختبار إصلاح progress-bar-container', 'info');
            logResult('✅ تم تحميل الصفحة بنجاح', 'success');
            
            // فحص تلقائي للعناصر
            setTimeout(() => {
                debugProgressBar('test-1');
                debugProgressBar('test-2');
            }, 1000);
        });
    </script>
</body>
</html>
