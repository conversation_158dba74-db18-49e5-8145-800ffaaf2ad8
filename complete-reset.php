<?php
/**
 * Complete System Reset
 * إعادة تعيين النظام بالكامل
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Reset - إعادة تعيين كاملة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 600px;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .status.success {
            background: #c6f6d5;
            color: #22543d;
            border: 2px solid #48bb78;
        }
        .status.info {
            background: #bee3f8;
            color: #2a4365;
            border: 2px solid #4299e1;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #48bb78);
            width: 0%;
            transition: width 0.5s ease;
        }
        .countdown {
            font-size: 24px;
            font-weight: bold;
            color: #4299e1;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 إعادة تعيين النظام بالكامل</h1>
        
        <div id="status" class="status info">
            جاري إعادة تعيين النظام...
        </div>
        
        <div class="progress">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        
        <div id="countdown" class="countdown"></div>
        
        <div style="margin-top: 30px; font-size: 14px; color: #666;">
            <p><strong>ما يتم إعادة تعيينه:</strong></p>
            <ul style="text-align: right;">
                <li>جميع بيانات localStorage</li>
                <li>جميع بيانات sessionStorage</li>
                <li>جميع ملفات تعريف الارتباط</li>
                <li>ذاكرة التخزين المؤقت</li>
                <li>بيانات الجلسة على الخادم</li>
            </ul>
        </div>
    </div>

    <script>
        let progress = 0;
        let step = 0;
        
        const steps = [
            { message: 'مسح localStorage...', action: clearLocalStorage },
            { message: 'مسح sessionStorage...', action: clearSessionStorage },
            { message: 'مسح ملفات تعريف الارتباط...', action: clearCookies },
            { message: 'مسح ذاكرة التخزين المؤقت...', action: clearCache },
            { message: 'إعادة تعيين بيانات الخادم...', action: clearServerSession },
            { message: 'تنظيف البيانات المتبقية...', action: finalCleanup },
            { message: 'اكتمل! إعادة التوجيه...', action: redirect }
        ];
        
        function updateProgress(percent, message) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('status').textContent = message;
        }
        
        function clearLocalStorage() {
            try {
                localStorage.clear();
                console.log('✅ localStorage cleared');
                return true;
            } catch (e) {
                console.error('❌ localStorage clear failed:', e);
                return false;
            }
        }
        
        function clearSessionStorage() {
            try {
                sessionStorage.clear();
                console.log('✅ sessionStorage cleared');
                return true;
            } catch (e) {
                console.error('❌ sessionStorage clear failed:', e);
                return false;
            }
        }
        
        function clearCookies() {
            try {
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                console.log('✅ Cookies cleared');
                return true;
            } catch (e) {
                console.error('❌ Cookies clear failed:', e);
                return false;
            }
        }
        
        async function clearCache() {
            try {
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    console.log('✅ Cache cleared');
                }
                return true;
            } catch (e) {
                console.error('❌ Cache clear failed:', e);
                return false;
            }
        }
        
        async function clearServerSession() {
            try {
                await fetch('/reset-session.php', { method: 'GET' });
                console.log('✅ Server session cleared');
                return true;
            } catch (e) {
                console.error('❌ Server session clear failed:', e);
                return true; // Continue anyway
            }
        }
        
        function finalCleanup() {
            try {
                // Clear any remaining data
                const keysToRemove = [
                    'currentNurseUser', 'userPreferences', 'lastLoginTime',
                    'childrenData', 'selectedChildId', 'lastSelectedChild',
                    'cachedChildren', 'cachedUsers', 'medicinesData',
                    'vaccinesData', 'contraceptivesData', 'messagesData'
                ];
                
                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                    sessionStorage.removeItem(key);
                });
                
                console.log('✅ Final cleanup completed');
                return true;
            } catch (e) {
                console.error('❌ Final cleanup failed:', e);
                return false;
            }
        }
        
        function redirect() {
            let countdown = 3;
            const countdownElement = document.getElementById('countdown');
            
            const timer = setInterval(() => {
                countdownElement.textContent = `إعادة التوجيه خلال ${countdown} ثانية...`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    window.location.href = '/';
                }
            }, 1000);
            
            return true;
        }
        
        async function runReset() {
            for (let i = 0; i < steps.length; i++) {
                const currentStep = steps[i];
                const progressPercent = ((i + 1) / steps.length) * 100;
                
                updateProgress(progressPercent, currentStep.message);
                
                try {
                    await currentStep.action();
                    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for visual effect
                } catch (e) {
                    console.error('Step failed:', currentStep.message, e);
                }
            }
            
            document.getElementById('status').className = 'status success';
            document.getElementById('status').textContent = '✅ تم إعادة تعيين النظام بنجاح!';
        }
        
        // Start the reset process automatically
        window.addEventListener('load', () => {
            setTimeout(runReset, 1000);
        });
    </script>
</body>
</html>
