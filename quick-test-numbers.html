<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ اختبار سريع للأرقام</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-box { 
            border: 2px solid #28a745; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .step {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        .result {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 10px 0;
        }
        button { 
            padding: 15px 30px; margin: 10px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
            font-size: 16px; font-weight: bold;
        }
        button:hover { background: #0056b3; }
        .big-button { font-size: 20px; padding: 20px 40px; }
    </style>
</head>
<body>
    <h1>⚡ اختبار سريع لحفظ الأرقام</h1>
    
    <div class="test-box">
        <h2>🎯 اختبار بخطوات بسيطة</h2>
        
        <div class="step">
            <h3>الخطوة 1: اختبار الصفحة الأصلية</h3>
            <p>اضغط الزر أدناه لفتح الصفحة الأصلية:</p>
            <button onclick="openOriginalPage()" class="big-button">🔗 فتح cs-manager.html</button>
        </div>
        
        <div class="step">
            <h3>الخطوة 2: إدخال أرقام تجريبية</h3>
            <p>في الصفحة الأصلية:</p>
            <ol>
                <li>اذهب لصفحة "💉 تدبير مخزون اللقاحات"</li>
                <li>أدخل هذه الأرقام بالضبط:</li>
                <ul>
                    <li><strong>HB1 في الشهر الأول:</strong> 123</li>
                    <li><strong>BCG في الشهر الأول:</strong> 456</li>
                    <li><strong>VPO في الشهر الثاني:</strong> 789</li>
                </ul>
                <li>اضغط "💾 حفظ التخطيط الشهري"</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>الخطوة 3: اختبار متصفح جديد</h3>
            <p>افتح متصفح جديد أو نافذة خفية وادخل على نفس الرابط:</p>
            <button onclick="openNewWindow()" class="big-button">🌐 فتح نافذة جديدة</button>
        </div>
        
        <div class="step">
            <h3>الخطوة 4: التحقق من النتائج</h3>
            <p>في المتصفح الجديد، تحقق من:</p>
            <ul>
                <li>✅ هل ظهر HB1 = 123؟</li>
                <li>✅ هل ظهر BCG = 456؟</li>
                <li>✅ هل ظهر VPO = 789؟</li>
            </ul>
        </div>
    </div>
    
    <div class="test-box">
        <h2>🔧 اختبار تقني مفصل</h2>
        <button onclick="openDetailedTest()">📊 فتح اختبار مفصل</button>
        <button onclick="testAPI()">🔌 اختبار API مباشر</button>
    </div>
    
    <div id="results" class="test-box" style="display: none;">
        <h2>📋 نتائج الاختبار</h2>
        <div id="testOutput"></div>
    </div>

    <script>
        function openOriginalPage() {
            window.open('cs-manager.html', '_blank');
            showResult('تم فتح الصفحة الأصلية في نافذة جديدة', 'result');
        }
        
        function openNewWindow() {
            window.open('cs-manager.html', '_blank');
            showResult('تم فتح نافذة جديدة - تحقق من ظهور الأرقام المحفوظة', 'result');
        }
        
        function openDetailedTest() {
            window.open('test-save-load-numbers.html', '_blank');
            showResult('تم فتح الاختبار المفصل', 'result');
        }
        
        async function testAPI() {
            showResult('جاري اختبار API...', 'step');
            
            try {
                // اختبار حفظ
                const saveResponse = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'save_monthly_planning',
                        user_id: 'quick_test_user',
                        center_id: '1',
                        planning: {
                            month1: {
                                name: 'اختبار سريع',
                                vaccines: { HB1: 999, BCG: 888 }
                            }
                        }
                    })
                });
                
                const saveData = await saveResponse.json();
                
                if (saveData.success) {
                    showResult('✅ نجح حفظ البيانات في API', 'result');
                    
                    // اختبار تحميل
                    const loadResponse = await fetch('vaccines-api.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            action: 'load_monthly_planning',
                            user_id: 'quick_test_user'
                        })
                    });
                    
                    const loadData = await loadResponse.json();
                    
                    if (loadData.success && loadData.planning.month1.vaccines.HB1 === 999) {
                        showResult('✅ نجح تحميل البيانات من API - الأرقام صحيحة!', 'result');
                        showResult(`البيانات المحملة: HB1=${loadData.planning.month1.vaccines.HB1}, BCG=${loadData.planning.month1.vaccines.BCG}`, 'result');
                    } else {
                        showResult('❌ فشل في تحميل البيانات الصحيحة من API', 'error');
                        showResult(`البيانات المحملة: ${JSON.stringify(loadData.planning?.month1?.vaccines || {})}`, 'error');
                    }
                } else {
                    showResult('❌ فشل في حفظ البيانات في API: ' + saveData.message, 'error');
                }
                
            } catch (error) {
                showResult('❌ خطأ في اختبار API: ' + error.message, 'error');
            }
        }
        
        function showResult(message, type) {
            const results = document.getElementById('results');
            const output = document.getElementById('testOutput');
            
            results.style.display = 'block';
            
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            
            output.appendChild(div);
            
            console.log(message);
        }
        
        // رسالة ترحيب
        window.onload = function() {
            showResult('مرحباً! استخدم الأزرار أعلاه لاختبار حفظ الأرقام', 'step');
        };
    </script>
</body>
</html>
