<?php
/**
 * Configure All APIs with Correct Database Credentials
 * تكوين جميع APIs بمعلومات قاعدة البيانات الصحيحة
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'database_credentials' => [
        'host' => 'localhost',
        'database' => 'csdb',
        'username' => 'csdbuser',
        'password' => 'kTVc4ERbgOLERL9B63R5'
    ],
    'configuration_steps' => [],
    'api_tests' => [],
    'success' => false
];

try {
    // Step 1: Test database connection with exact credentials
    $response['configuration_steps'][] = 'Testing database connection...';
    
    $dsn = "mysql:host=localhost;dbname=csdb;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, 'csdbuser', 'kTVc4ERbgOLERL9B63R5', $options);
    $response['configuration_steps'][] = '✅ Database connection successful';
    
    // Step 2: Verify tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $response['configuration_steps'][] = "✅ Found " . count($tables) . " tables in database";
    
    // Step 3: Create missing tables that APIs expect
    $missing_tables = [];
    
    // Check for medicine_list (expected by medicines-api.php)
    if (!in_array('medicine_list', $tables)) {
        $pdo->exec("CREATE TABLE medicine_list (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50),
            center_id INT,
            name VARCHAR(255) NOT NULL,
            unit VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        $missing_tables[] = 'medicine_list';
    }
    
    // Check for vaccine_list (expected by vaccines-api.php)
    if (!in_array('vaccine_list', $tables)) {
        $pdo->exec("CREATE TABLE vaccine_list (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50),
            center_id INT,
            name VARCHAR(255) NOT NULL,
            quantity INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        $missing_tables[] = 'vaccine_list';
    }
    
    // Check for contraceptive_list (expected by family-planning-api.php)
    if (!in_array('contraceptive_list', $tables)) {
        $pdo->exec("CREATE TABLE contraceptive_list (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50),
            center_id INT,
            name VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        $missing_tables[] = 'contraceptive_list';
    }
    
    // Check for medicine_monthly_planning
    if (!in_array('medicine_monthly_planning', $tables)) {
        $pdo->exec("CREATE TABLE medicine_monthly_planning (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(50),
            center_id INT,
            month_key VARCHAR(20),
            month_name VARCHAR(50),
            medicines_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_month (user_id, month_key)
        )");
        $missing_tables[] = 'medicine_monthly_planning';
    }
    
    if (count($missing_tables) > 0) {
        $response['configuration_steps'][] = "✅ Created missing tables: " . implode(', ', $missing_tables);
    } else {
        $response['configuration_steps'][] = "✅ All required tables already exist";
    }
    
    // Step 4: Populate sample data if tables are empty
    $sample_data_added = [];
    
    // Add sample medicines if medicine_list is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM medicine_list");
    if ($stmt->fetchColumn() == 0) {
        $sample_medicines = [
            ['id' => 'med_001', 'name' => 'باراسيتامول', 'unit' => '500mg'],
            ['id' => 'med_002', 'name' => 'إيبوبروفين', 'unit' => '400mg'],
            ['id' => 'med_003', 'name' => 'أموكسيسيلين', 'unit' => '250mg']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO medicine_list (id, name, unit) VALUES (?, ?, ?)");
        foreach ($sample_medicines as $med) {
            $stmt->execute([$med['id'], $med['name'], $med['unit']]);
        }
        $sample_data_added[] = 'medicines';
    }
    
    // Add sample vaccines if vaccine_list is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM vaccine_list");
    if ($stmt->fetchColumn() == 0) {
        $sample_vaccines = [
            ['id' => 'vac_001', 'name' => 'BCG', 'quantity' => 50],
            ['id' => 'vac_002', 'name' => 'شلل الأطفال', 'quantity' => 45],
            ['id' => 'vac_003', 'name' => 'الحصبة', 'quantity' => 40]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO vaccine_list (id, name, quantity) VALUES (?, ?, ?)");
        foreach ($sample_vaccines as $vac) {
            $stmt->execute([$vac['id'], $vac['name'], $vac['quantity']]);
        }
        $sample_data_added[] = 'vaccines';
    }
    
    if (count($sample_data_added) > 0) {
        $response['configuration_steps'][] = "✅ Added sample data for: " . implode(', ', $sample_data_added);
    }
    
    // Step 5: Test each API endpoint
    $api_files = [
        'children-api.php' => 'load',
        'medicines-api.php' => 'test',
        'vaccines-api.php' => 'load_vaccines',
        'family-planning-api.php' => 'test',
        'user-api-simple.php' => 'test'
    ];
    
    $response['configuration_steps'][] = 'Testing API endpoints...';
    
    foreach ($api_files as $api_file => $action) {
        try {
            $test_data = ['action' => $action, 'user_id' => 'test_user'];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/$api_file");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $result = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $api_result = json_decode($result, true);
            $status = ($http_code == 200 && $api_result && isset($api_result['success'])) ? 'SUCCESS' : 'FAILED';
            
            $response['api_tests'][$api_file] = [
                'status' => $status,
                'http_code' => $http_code,
                'response' => $api_result
            ];
            
        } catch (Exception $e) {
            $response['api_tests'][$api_file] = [
                'status' => 'ERROR',
                'error' => $e->getMessage()
            ];
        }
    }
    
    $successful_apis = array_filter($response['api_tests'], function($test) {
        return $test['status'] === 'SUCCESS';
    });
    
    $response['configuration_steps'][] = "✅ " . count($successful_apis) . "/" . count($api_files) . " APIs working correctly";
    
    $response['success'] = true;
    $response['message'] = 'Database and APIs configured successfully';
    $response['next_steps'] = [
        'Clear browser cache and localStorage',
        'Visit https://www.csmanager.online/',
        'Login with: admin / password',
        'Test adding children, medicines, vaccines'
    ];
    
} catch (Exception $e) {
    $response['error'] = $e->getMessage();
    $response['message'] = 'Configuration failed: ' . $e->getMessage();
    $response['troubleshooting'] = [
        'Check if MySQL service is running',
        'Verify database credentials are correct',
        'Ensure csdbuser has proper permissions on csdb database'
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
