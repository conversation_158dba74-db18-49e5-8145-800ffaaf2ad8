<?php
/**
 * إعدادات قاعدة البيانات الجديدة
 * New Database Configuration
 */

class Database {
    // ضع بيانات قاعدة البيانات الصحيحة هنا
    private $host = 'localhost';
    private $db_name = 'csdb';
    private $username = 'csdbuser';
    private $password = 'kTVc4ERbgOLERL9B63R5';
    private $charset = 'utf8mb4';
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            // محاولة الاتصال مع معلومات مفصلة عن الأخطاء
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
            // اختبار الاتصال
            $this->conn->query("SELECT 1");
            
        } catch(PDOException $exception) {
            // تسجيل تفصيلي للخطأ
            $error_details = [
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'host' => $this->host,
                'database' => $this->db_name,
                'username' => $this->username,
                'dsn' => $dsn ?? 'غير محدد'
            ];
            
            error_log("Database connection error: " . json_encode($error_details));
            
            // رسالة خطأ مفصلة للمطور
            if (isset($_GET['debug']) && $_GET['debug'] === 'true') {
                throw new Exception("تفاصيل خطأ الاتصال: " . $exception->getMessage() . " | الكود: " . $exception->getCode());
            } else {
                throw new Exception("فشل في الاتصال بقاعدة البيانات");
            }
        }

        return $this->conn;
    }

    /**
     * اختبار الاتصال مع تفاصيل
     */
    public function testConnection() {
        try {
            $this->getConnection();
            return [
                'success' => true,
                'message' => 'تم الاتصال بنجاح',
                'host' => $this->host,
                'database' => $this->db_name,
                'username' => $this->username
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'host' => $this->host,
                'database' => $this->db_name,
                'username' => $this->username
            ];
        }
    }

    /**
     * فحص وجود الجداول
     */
    public function checkTablesExist() {
        $required_tables = [
            'centers', 'users', 'children', 'vaccines', 'vaccine_stock',
            'child_vaccinations', 'vaccine_usage_log', 'medicines', 'medicine_stock',
            'contraceptives', 'contraceptive_stock', 'monthly_planning',
            'messages', 'tasks', 'notifications', 'monthly_stats', 'user_settings'
        ];

        try {
            $stmt = $this->conn->query("SHOW TABLES");
            $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $missing_tables = array_diff($required_tables, $existing_tables);
            
            return [
                'all_exist' => empty($missing_tables),
                'existing' => $existing_tables,
                'missing' => $missing_tables,
                'total_required' => count($required_tables),
                'total_existing' => count($existing_tables)
            ];
            
        } catch(PDOException $exception) {
            error_log("Error checking tables: " . $exception->getMessage());
            return [
                'all_exist' => false,
                'error' => $exception->getMessage()
            ];
        }
    }

    /**
     * إنشاء قاعدة البيانات والجداول
     */
    public function createDatabase() {
        try {
            $sql_file = __DIR__ . '/../database_design.sql';
            if (!file_exists($sql_file)) {
                throw new Exception("ملف قاعدة البيانات غير موجود: " . $sql_file);
            }

            $sql = file_get_contents($sql_file);
            
            // تنظيف وتقسيم الاستعلامات
            $sql = preg_replace('/--.*$/m', '', $sql); // إزالة التعليقات
            $queries = array_filter(array_map('trim', explode(';', $sql)));
            
            $executed = 0;
            foreach ($queries as $query) {
                if (!empty($query)) {
                    $this->conn->exec($query);
                    $executed++;
                }
            }

            return [
                'success' => true,
                'queries_executed' => $executed,
                'message' => "تم تنفيذ {$executed} استعلام بنجاح"
            ];
            
        } catch(PDOException $exception) {
            error_log("Database creation error: " . $exception->getMessage());
            return [
                'success' => false,
                'error' => $exception->getMessage()
            ];
        }
    }
}

// اختبار سريع للاتصال
if (isset($_GET['test']) && $_GET['test'] === 'true') {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $db = new Database();
        $result = $db->testConnection();
        
        if ($result['success']) {
            $tables_check = $db->checkTablesExist();
            $result['tables'] = $tables_check;
        }
        
        echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}
?>
