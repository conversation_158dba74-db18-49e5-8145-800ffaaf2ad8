<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 تشخيص اتصال API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .debug-section { 
            border: 2px solid #dc3545; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.test { background: #dc3545; }
        button.test:hover { background: #c82333; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { 
            background: #f8f9fa; padding: 15px; border-radius: 5px; 
            overflow-x: auto; font-size: 12px; white-space: pre-wrap;
        }
        .response-details {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 تشخيص اتصال API وقاعدة البيانات</h1>
    
    <div class="debug-section">
        <h2>🧪 اختبارات التشخيص</h2>
        <button onclick="testBasicConnection()" class="test">اختبار الاتصال الأساسي</button>
        <button onclick="testDatabaseConnection()">اختبار قاعدة البيانات</button>
        <button onclick="testVaccinesAPI()">اختبار vaccines-api.php</button>
        <button onclick="testJSONResponse()">اختبار استجابة JSON</button>
        <button onclick="runFullDiagnosis()" class="test">تشخيص شامل</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز للتشخيص
    </div>
    
    <div class="debug-section">
        <h3>📊 تفاصيل الاستجابة</h3>
        <div id="responseDetails"></div>
    </div>
    
    <div class="debug-section">
        <h3>📝 سجل التشخيص</h3>
        <pre id="debugLog">بدء التشخيص...</pre>
    </div>

    <script>
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            debugLog.push(logEntry);
            
            document.getElementById('debugLog').textContent = debugLog.join('\n');
            
            updateStatus(message, type);
            console.log(logEntry);
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
        }
        
        function showResponseDetails(response, data, error = null) {
            const details = document.getElementById('responseDetails');
            
            let html = `
                <h4>تفاصيل الاستجابة:</h4>
                <p><strong>الحالة:</strong> ${response?.status || 'غير معروف'}</p>
                <p><strong>نوع المحتوى:</strong> ${response?.headers?.get('content-type') || 'غير معروف'}</p>
                <p><strong>حجم الاستجابة:</strong> ${JSON.stringify(data || {}).length} حرف</p>
            `;
            
            if (error) {
                html += `<p><strong>الخطأ:</strong> ${error}</p>`;
            }
            
            if (data) {
                html += `<h5>البيانات:</h5><pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            details.innerHTML = html;
        }
        
        async function testBasicConnection() {
            log('بدء اختبار الاتصال الأساسي...', 'info');
            
            try {
                const response = await fetch('test-api-response.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });
                
                log(`حالة الاستجابة: ${response.status}`, 'info');
                
                const text = await response.text();
                log(`نص الاستجابة: ${text.substring(0, 200)}...`, 'info');
                
                const data = JSON.parse(text);
                showResponseDetails(response, data);
                
                if (data.success) {
                    log('✅ الاتصال الأساسي ناجح', 'pass');
                } else {
                    log('❌ فشل الاتصال الأساسي: ' + data.message, 'fail');
                }
                
            } catch (error) {
                log('❌ خطأ في الاتصال الأساسي: ' + error.message, 'fail');
                showResponseDetails(null, null, error.message);
            }
        }
        
        async function testDatabaseConnection() {
            log('بدء اختبار قاعدة البيانات...', 'info');
            
            try {
                const response = await fetch('test-api-response.php');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const text = await response.text();
                log(`استجابة قاعدة البيانات: ${text}`, 'info');
                
                const data = JSON.parse(text);
                showResponseDetails(response, data);
                
                if (data.success) {
                    log('✅ اتصال قاعدة البيانات ناجح', 'pass');
                } else {
                    log('❌ فشل اتصال قاعدة البيانات: ' + data.message, 'fail');
                }
                
            } catch (error) {
                log('❌ خطأ في اختبار قاعدة البيانات: ' + error.message, 'fail');
                showResponseDetails(null, null, error.message);
            }
        }
        
        async function testVaccinesAPI() {
            log('بدء اختبار vaccines-api.php...', 'info');
            
            try {
                const requestData = {
                    action: 'load_monthly_planning',
                    user_id: 'test_user_debug'
                };
                
                log(`إرسال البيانات: ${JSON.stringify(requestData)}`, 'info');
                
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                log(`حالة استجابة vaccines-api: ${response.status}`, 'info');
                log(`نوع المحتوى: ${response.headers.get('content-type')}`, 'info');
                
                const text = await response.text();
                log(`نص الاستجابة: ${text}`, 'info');
                
                if (!text.trim()) {
                    throw new Error('الاستجابة فارغة');
                }
                
                const data = JSON.parse(text);
                showResponseDetails(response, data);
                
                if (data.success) {
                    log('✅ vaccines-api.php يعمل بشكل صحيح', 'pass');
                } else {
                    log('⚠️ vaccines-api.php يعمل لكن مع رسالة: ' + data.message, 'warning');
                }
                
            } catch (error) {
                log('❌ خطأ في vaccines-api.php: ' + error.message, 'fail');
                showResponseDetails(null, null, error.message);
            }
        }
        
        async function testJSONResponse() {
            log('بدء اختبار استجابة JSON...', 'info');
            
            try {
                const response = await fetch('vaccines-api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'load_monthly_planning',
                        user_id: 'json_test_user'
                    })
                });
                
                // قراءة الاستجابة كنص أولاً
                const responseText = await response.text();
                log(`نص الاستجابة الخام: "${responseText}"`, 'info');
                
                if (!responseText.trim()) {
                    throw new Error('الاستجابة فارغة تماماً');
                }
                
                // محاولة تحليل JSON
                let jsonData;
                try {
                    jsonData = JSON.parse(responseText);
                    log('✅ تم تحليل JSON بنجاح', 'pass');
                } catch (jsonError) {
                    log('❌ فشل في تحليل JSON: ' + jsonError.message, 'fail');
                    log(`أول 100 حرف من الاستجابة: "${responseText.substring(0, 100)}"`, 'fail');
                    throw jsonError;
                }
                
                showResponseDetails(response, jsonData);
                
                if (jsonData.success !== undefined) {
                    log('✅ هيكل JSON صحيح', 'pass');
                } else {
                    log('⚠️ هيكل JSON غير متوقع', 'warning');
                }
                
            } catch (error) {
                log('❌ خطأ في اختبار JSON: ' + error.message, 'fail');
                showResponseDetails(null, null, error.message);
            }
        }
        
        async function runFullDiagnosis() {
            log('=== بدء التشخيص الشامل ===', 'info');
            
            await testBasicConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDatabaseConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testVaccinesAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testJSONResponse();
            
            log('=== انتهى التشخيص الشامل ===', 'info');
        }
        
        // بدء التشخيص التلقائي
        window.onload = function() {
            log('تم تحميل صفحة التشخيص', 'info');
            
            setTimeout(() => {
                testBasicConnection();
            }, 1000);
        };
    </script>
</body>
</html>
