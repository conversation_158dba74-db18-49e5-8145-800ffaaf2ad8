<?php
/**
 * اختبار تحميل التخطيط الشهري
 */

header('Content-Type: application/json; charset=utf-8');

// محاكاة طلب تحميل التخطيط الشهري
$testData = json_encode([
    'action' => 'load_monthly_planning',
    'user_id' => 'test_user'
]);

// إرسال الطلب إلى vaccines-api.php
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.csmanager.online/vaccines-api.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($testData)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo json_encode([
    'test_request' => json_decode($testData, true),
    'http_code' => $httpCode,
    'curl_error' => $error,
    'response' => $response,
    'parsed_response' => json_decode($response, true)
], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
