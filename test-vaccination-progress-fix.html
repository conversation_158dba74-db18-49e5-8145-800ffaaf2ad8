<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح شريط التقدم للتلقيحات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .progress-bar-container {
            background: #e9ecef;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            background: #28a745;
            height: 100%;
            transition: width 0.5s ease-in-out;
            border-radius: 10px;
        }
        
        .child-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background: white;
        }
        
        .vaccination-progress-container {
            margin: 10px 0;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار إصلاح شريط التقدم للتلقيحات</h1>
        
        <div class="test-section">
            <h3>📋 معلومات الاختبار</h3>
            <p>هذا الاختبار يتحقق من أن شريط التقدم للتلقيحات يتحدث بشكل صحيح عند:</p>
            <ul>
                <li>إكمال تلقيح جديد</li>
                <li>إلغاء تلقيح مكتمل</li>
                <li>تحديث البيانات من قاعدة البيانات</li>
                <li>عرض قائمة الأطفال</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎯 اختبار شريط التقدم</h3>
            <div id="testChild" class="child-card" data-child-id="test-child-1">
                <h4>الطفل التجريبي: أحمد محمد</h4>
                <div class="vaccination-progress-container">
                    <div class="progress-info">
                        <span>التلقيحات المنجزة:</span>
                        <span class="progress-count">0/12</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: 0%"></div>
                    </div>
                    <div class="progress-percentage">0%</div>
                </div>
                <div>
                    <button class="btn btn-success" onclick="simulateVaccinationComplete()">✅ إكمال تلقيح</button>
                    <button class="btn btn-danger" onclick="simulateVaccinationCancel()">❌ إلغاء تلقيح</button>
                    <button class="btn" onclick="resetProgress()">🔄 إعادة تعيين</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>🔧 اختبار الدوال المحدثة</h3>
            <button class="btn" onclick="testUpdateProgressBarForChild()">اختبار updateProgressBarForChild</button>
            <button class="btn" onclick="testUpdateAllProgressBars()">اختبار updateAllProgressBars</button>
            <button class="btn" onclick="testDataSynchronization()">اختبار مزامنة البيانات</button>
        </div>
    </div>

    <script>
        // بيانات تجريبية
        let testChildData = {
            id: 'test-child-1',
            name: 'أحمد محمد',
            completedVaccinations: {},
            vaccinationDates: new Array(12).fill(null).map((_, i) => ({ index: i, name: `تلقيح ${i + 1}` }))
        };

        let completedCount = 0;

        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `test-result ${type}`;
            resultElement.innerHTML = `${new Date().toLocaleTimeString('ar-SA')} - ${message}`;
            resultsDiv.appendChild(resultElement);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function updateProgressDisplay() {
            const progressBar = document.querySelector('.progress-bar');
            const progressCount = document.querySelector('.progress-count');
            const progressPercentage = document.querySelector('.progress-percentage');
            
            const totalCount = testChildData.vaccinationDates.length;
            const progressPercent = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;
            
            if (progressBar) {
                progressBar.style.width = `${progressPercent}%`;
                logResult(`تم تحديث شريط التقدم: العرض = ${progressPercent}%`, 'success');
            }
            
            if (progressCount) {
                progressCount.textContent = `${completedCount}/${totalCount}`;
            }
            
            if (progressPercentage) {
                progressPercentage.textContent = `${progressPercent}%`;
            }
        }

        function simulateVaccinationComplete() {
            if (completedCount < 12) {
                completedCount++;
                testChildData.completedVaccinations[completedCount - 1] = true;
                updateProgressDisplay();
                logResult(`تم إكمال تلقيح جديد. العدد الحالي: ${completedCount}/12`, 'success');
            } else {
                logResult('جميع التلقيحات مكتملة بالفعل', 'info');
            }
        }

        function simulateVaccinationCancel() {
            if (completedCount > 0) {
                delete testChildData.completedVaccinations[completedCount - 1];
                completedCount--;
                updateProgressDisplay();
                logResult(`تم إلغاء تلقيح. العدد الحالي: ${completedCount}/12`, 'error');
            } else {
                logResult('لا توجد تلقيحات لإلغائها', 'info');
            }
        }

        function resetProgress() {
            completedCount = 0;
            testChildData.completedVaccinations = {};
            updateProgressDisplay();
            logResult('تم إعادة تعيين التقدم إلى الصفر', 'info');
        }

        function testUpdateProgressBarForChild() {
            logResult('🧪 اختبار دالة updateProgressBarForChild...', 'info');
            
            // محاكاة الدالة
            const child = testChildData;
            const completedVaccinations = child.completedVaccinations || {};
            const completedCount = Object.values(completedVaccinations).filter(status => status === true).length;
            const totalCount = child.vaccinationDates ? child.vaccinationDates.length : 12;
            const progressPercent = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

            logResult(`✅ حساب التقدم: ${completedCount}/${totalCount} = ${progressPercent}%`, 'success');
            
            // تحديث العرض
            updateProgressDisplay();
        }

        function testUpdateAllProgressBars() {
            logResult('🧪 اختبار دالة updateAllProgressBars...', 'info');
            logResult('✅ تم تحديث جميع أشرطة التقدم (محاكاة)', 'success');
        }

        function testDataSynchronization() {
            logResult('🧪 اختبار مزامنة البيانات...', 'info');
            
            // محاكاة تحديث من قاعدة البيانات
            const previousData = { ...testChildData.completedVaccinations };
            
            // إضافة تلقيح جديد من "قاعدة البيانات"
            testChildData.completedVaccinations[5] = true;
            completedCount = Object.values(testChildData.completedVaccinations).filter(status => status === true).length;
            
            const hasChanges = JSON.stringify(previousData) !== JSON.stringify(testChildData.completedVaccinations);
            
            if (hasChanges) {
                updateProgressDisplay();
                logResult('✅ تم اكتشاف تغييرات في البيانات وتحديث العرض', 'success');
            } else {
                logResult('ℹ️ لا توجد تغييرات في البيانات', 'info');
            }
        }

        // تهيئة الاختبار
        document.addEventListener('DOMContentLoaded', function() {
            logResult('🚀 بدء اختبار إصلاح شريط التقدم للتلقيحات', 'info');
            logResult('✅ تم تحميل الصفحة بنجاح', 'success');
        });
    </script>
</body>
</html>
