<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحديث عناصر stock-item</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f8f9fa;
        }
        .test-section { 
            border: 2px solid #007bff; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .stock-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .stock-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .stock-item h5 {
            margin: 0 0 5px 0;
            color: #28a745;
            font-size: 1.1rem;
        }
        .stock-input { 
            width: 80px; 
            padding: 8px; 
            margin: 0 10px;
            border: 2px solid #ced4da;
            border-radius: 4px;
            text-align: center;
        }
        .stock-input:focus {
            border-color: #007bff;
            outline: none;
        }
        .month-name-input { 
            width: 200px; 
            padding: 10px; 
            margin: 10px 0;
            border: 2px solid #ced4da;
            border-radius: 5px;
            font-size: 16px;
        }
        button { 
            padding: 10px 20px; 
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto;
            font-size: 12px;
        }
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .month-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار تحديث عناصر stock-item</h1>
    
    <div class="test-section">
        <h2>🎯 هدف الاختبار</h2>
        <p>التحقق من أن عناصر <code>.stock-item</code> تتحدث في الواجهة وقاعدة البيانات عند:</p>
        <ul>
            <li>✅ إضافة كميات اللقاحات</li>
            <li>✅ تغيير أسماء الأشهر</li>
            <li>✅ إعادة تحميل الصفحة</li>
            <li>✅ حفظ واستعادة البيانات</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔧 أدوات التحكم</h2>
        <button onclick="initializeTest()">تهيئة الاختبار</button>
        <button onclick="addTestData()">إضافة بيانات تجريبية</button>
        <button onclick="updateDisplay()">تحديث العرض</button>
        <button onclick="saveToStorage()">حفظ في التخزين</button>
        <button onclick="loadFromStorage()">تحميل من التخزين</button>
        <button onclick="clearAll()">مسح الكل</button>
        <button onclick="simulateAPICall()">محاكاة API</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز للاختبار
    </div>
    
    <div class="grid-container">
        <div class="month-section">
            <h3>الشهر الأول</h3>
            <input type="text" class="month-name-input" id="month1Name" 
                   placeholder="اسم الشهر الأول" 
                   onchange="updateMonthName('month1')" 
                   onblur="updateMonthName('month1')">
            <div id="month1Grid"></div>
        </div>
        
        <div class="month-section">
            <h3>الشهر الثاني</h3>
            <input type="text" class="month-name-input" id="month2Name" 
                   placeholder="اسم الشهر الثاني"
                   onchange="updateMonthName('month2')" 
                   onblur="updateMonthName('month2')">
            <div id="month2Grid"></div>
        </div>
        
        <div class="month-section">
            <h3>الشهر الثالث</h3>
            <input type="text" class="month-name-input" id="month3Name" 
                   placeholder="اسم الشهر الثالث"
                   onchange="updateMonthName('month3')" 
                   onblur="updateMonthName('month3')">
            <div id="month3Grid"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 البيانات الحالية</h2>
        <pre id="dataDisplay">لا توجد بيانات</pre>
    </div>
    
    <div class="test-section">
        <h2>📝 سجل الأحداث</h2>
        <div id="eventLog" style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;"></div>
    </div>

    <script>
        let monthlyPlanning = {};
        let currentUser = { id: 'test_user' };
        let eventCounter = 0;
        
        const vaccineDefinitions = {
            'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B' },
            'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)' },
            'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)' },
            'VPI': { name: 'شلل الأطفال المحقون', frenchName: 'VPI (Polio Injectable)' },
            'Rota': { name: 'الروتا', frenchName: 'Rotavirus' },
            'Penta': { name: 'الخماسي', frenchName: 'Pentavalent' },
            'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)' },
            'Pneumo': { name: 'المكورات الرئوية', frenchName: 'Pneumocoque' },
            'DTC': { name: 'الثلاثي', frenchName: 'DTC (Diphtérie-Tétanos-Coqueluche)' },
            'VAT': { name: 'الكزاز للحوامل', frenchName: 'VAT (Tétanos femmes enceintes)' }
        };
        
        function logEvent(message, type = 'info') {
            eventCounter++;
            const timestamp = new Date().toLocaleTimeString();
            const log = document.getElementById('eventLog');
            const entry = document.createElement('div');
            entry.style.cssText = `margin: 5px 0; padding: 5px; border-radius: 3px; background: ${
                type === 'success' ? '#d4edda' : 
                type === 'error' ? '#f8d7da' : 
                type === 'warning' ? '#fff3cd' : '#d1ecf1'
            };`;
            entry.innerHTML = `<strong>${eventCounter}.</strong> [${timestamp}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
            
            updateStatus(message, type);
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
        }
        
        function initializeTest() {
            const defaultVaccines = {};
            Object.keys(vaccineDefinitions).forEach(key => {
                defaultVaccines[key] = 0;
            });
            
            monthlyPlanning = {
                month1: { name: '', vaccines: {...defaultVaccines} },
                month2: { name: '', vaccines: {...defaultVaccines} },
                month3: { name: '', vaccines: {...defaultVaccines} }
            };
            
            updateDisplay();
            showCurrentData();
            logEvent('تم تهيئة الاختبار بنجاح', 'success');
        }
        
        function updateDisplay() {
            ['month1', 'month2', 'month3'].forEach(month => {
                const grid = document.getElementById(`${month}Grid`);
                grid.innerHTML = '';
                
                if (!monthlyPlanning[month]) {
                    monthlyPlanning[month] = { name: '', vaccines: {} };
                }
                
                Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                    const vaccine = vaccineDefinitions[vaccineKey];
                    const currentValue = monthlyPlanning[month].vaccines[vaccineKey] || 0;
                    
                    const vaccineItem = document.createElement('div');
                    vaccineItem.className = 'stock-item';
                    vaccineItem.innerHTML = `
                        <h5>${vaccine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; font-style: italic; margin-bottom: 8px;">
                            ${vaccine.frenchName}
                        </div>
                        <div class="stock-controls">
                            <input type="number" class="stock-input" id="${month}_${vaccineKey}"
                                   value="${currentValue}" min="0" max="1000"
                                   onchange="updateVaccine('${month}', '${vaccineKey}')"
                                   onblur="updateVaccine('${month}', '${vaccineKey}')">
                            <span>قارورة</span>
                        </div>
                    `;
                    grid.appendChild(vaccineItem);
                });
            });
            
            logEvent(`تم تحديث العرض - ${Object.keys(vaccineDefinitions).length} لقاح × 3 أشهر`, 'info');
        }
        
        async function updateVaccine(month, vaccineKey) {
            const input = document.getElementById(`${month}_${vaccineKey}`);
            const value = parseInt(input.value) || 0;
            
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].vaccines[vaccineKey] = value;
            
            // محاكاة حفظ في localStorage
            localStorage.setItem(`monthlyPlanning_${currentUser.id}`, JSON.stringify(monthlyPlanning));
            
            // محاكاة API call
            await simulateAPICall(`update_vaccine_${month}_${vaccineKey}`, { month, vaccineKey, value });
            
            showCurrentData();
            logEvent(`تم تحديث ${vaccineKey} في ${month}: ${value} قارورة`, 'success');
        }
        
        async function updateMonthName(month) {
            const input = document.getElementById(`${month}Name`);
            const monthName = input.value.trim();
            
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].name = monthName;
            
            // محاكاة حفظ في localStorage
            localStorage.setItem(`monthlyPlanning_${currentUser.id}`, JSON.stringify(monthlyPlanning));
            
            // محاكاة API call
            await simulateAPICall(`update_month_name_${month}`, { month, name: monthName });
            
            showCurrentData();
            logEvent(`تم تحديث اسم ${month}: "${monthName}"`, 'success');
        }
        
        function addTestData() {
            // إضافة أسماء الأشهر
            document.getElementById('month1Name').value = 'يناير 2025';
            document.getElementById('month2Name').value = 'فبراير 2025';
            document.getElementById('month3Name').value = 'مارس 2025';
            
            // إضافة كميات تجريبية
            const testData = {
                month1: { HB1: 50, BCG: 30, VPO: 25 },
                month2: { HB1: 60, Penta: 40, RR: 35 },
                month3: { DTC: 45, VAT: 20, Pneumo: 30 }
            };
            
            Object.keys(testData).forEach(month => {
                Object.keys(testData[month]).forEach(vaccine => {
                    const input = document.getElementById(`${month}_${vaccine}`);
                    if (input) {
                        input.value = testData[month][vaccine];
                        updateVaccine(month, vaccine);
                    }
                });
                updateMonthName(month);
            });
            
            logEvent('تم إضافة البيانات التجريبية', 'success');
        }
        
        function saveToStorage() {
            localStorage.setItem(`monthlyPlanning_${currentUser.id}`, JSON.stringify(monthlyPlanning));
            logEvent('تم حفظ البيانات في localStorage', 'success');
        }
        
        function loadFromStorage() {
            try {
                const saved = localStorage.getItem(`monthlyPlanning_${currentUser.id}`);
                if (saved) {
                    monthlyPlanning = JSON.parse(saved);
                    
                    // تحديث أسماء الأشهر
                    document.getElementById('month1Name').value = monthlyPlanning.month1?.name || '';
                    document.getElementById('month2Name').value = monthlyPlanning.month2?.name || '';
                    document.getElementById('month3Name').value = monthlyPlanning.month3?.name || '';
                    
                    updateDisplay();
                    showCurrentData();
                    logEvent('تم تحميل البيانات من localStorage', 'success');
                } else {
                    logEvent('لا توجد بيانات محفوظة', 'warning');
                }
            } catch (error) {
                logEvent('خطأ في تحميل البيانات: ' + error.message, 'error');
            }
        }
        
        function clearAll() {
            monthlyPlanning = {};
            document.getElementById('month1Name').value = '';
            document.getElementById('month2Name').value = '';
            document.getElementById('month3Name').value = '';
            
            ['month1Grid', 'month2Grid', 'month3Grid'].forEach(gridId => {
                document.getElementById(gridId).innerHTML = '';
            });
            
            localStorage.removeItem(`monthlyPlanning_${currentUser.id}`);
            
            showCurrentData();
            logEvent('تم مسح جميع البيانات', 'warning');
        }
        
        async function simulateAPICall(action, data) {
            return new Promise(resolve => {
                setTimeout(() => {
                    console.log(`API Call: ${action}`, data);
                    resolve({ success: true, message: 'تم الحفظ بنجاح' });
                }, 100);
            });
        }
        
        function showCurrentData() {
            const display = document.getElementById('dataDisplay');
            display.textContent = JSON.stringify(monthlyPlanning, null, 2);
        }
        
        // تهيئة تلقائية
        window.onload = function() {
            initializeTest();
            logEvent('تم تحميل صفحة الاختبار', 'info');
        };
        
        // مراقبة التغييرات
        setInterval(() => {
            const hasData = Object.keys(monthlyPlanning).length > 0;
            if (hasData) {
                const totalVaccines = Object.values(monthlyPlanning).reduce((total, month) => {
                    return total + Object.values(month.vaccines || {}).reduce((sum, qty) => sum + qty, 0);
                }, 0);
                
                if (totalVaccines > 0) {
                    document.title = `اختبار stock-item (${totalVaccines} قارورة)`;
                }
            }
        }, 2000);
    </script>
</body>
</html>
