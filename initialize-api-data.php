<?php
/**
 * API Data Initialization Script
 * سكريبت تهيئة بيانات API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

require_once 'config/database-live.php';

$response = [
    'success' => false,
    'message' => '',
    'timestamp' => date('Y-m-d H:i:s'),
    'initialized' => []
];

try {
    $pdo = getDatabaseConnection();
    
    // Initialize default medicines
    $medicines = [
        ['name' => 'باراسيتامول', 'type' => 'مسكن', 'dosage' => '500mg', 'stock' => 100],
        ['name' => 'إيبوبروفين', 'type' => 'مضاد التهاب', 'dosage' => '400mg', 'stock' => 80],
        ['name' => 'أموكسيسيلين', 'type' => 'مضاد حيوي', 'dosage' => '250mg', 'stock' => 60],
        ['name' => 'أسبرين', 'type' => 'مسكن', 'dosage' => '100mg', 'stock' => 120],
        ['name' => 'سيتريزين', 'type' => 'مضاد حساسية', 'dosage' => '10mg', 'stock' => 90],
        ['name' => 'أوميبرازول', 'type' => 'مضاد حموضة', 'dosage' => '20mg', 'stock' => 70],
        ['name' => 'ميتفورمين', 'type' => 'مضاد سكري', 'dosage' => '500mg', 'stock' => 85],
        ['name' => 'أتورفاستاتين', 'type' => 'مضاد كوليسترول', 'dosage' => '20mg', 'stock' => 65]
    ];
    
    // Check if medicines table exists and create if needed
    $pdo->exec("CREATE TABLE IF NOT EXISTS medicines (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100),
        dosage VARCHAR(50),
        stock INT DEFAULT 0,
        min_stock INT DEFAULT 10,
        expiry_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Insert medicines if table is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM medicines");
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("INSERT INTO medicines (name, type, dosage, stock) VALUES (?, ?, ?, ?)");
        foreach ($medicines as $medicine) {
            $stmt->execute([$medicine['name'], $medicine['type'], $medicine['dosage'], $medicine['stock']]);
        }
        $response['initialized'][] = 'medicines (' . count($medicines) . ' items)';
    }
    
    // Initialize contraceptives
    $contraceptives = [
        ['name' => 'حبوب منع الحمل المركبة', 'type' => 'هرموني', 'duration' => '21 يوم', 'stock' => 50],
        ['name' => 'حبوب البروجستين فقط', 'type' => 'هرموني', 'duration' => '28 يوم', 'stock' => 40],
        ['name' => 'حقن ديبو بروفيرا', 'type' => 'هرموني', 'duration' => '3 أشهر', 'stock' => 30],
        ['name' => 'اللولب النحاسي', 'type' => 'جهاز', 'duration' => '10 سنوات', 'stock' => 20],
        ['name' => 'اللولب الهرموني', 'type' => 'جهاز', 'duration' => '5 سنوات', 'stock' => 15],
        ['name' => 'الواقي الذكري', 'type' => 'حاجز', 'duration' => 'استخدام واحد', 'stock' => 200],
        ['name' => 'الواقي الأنثوي', 'type' => 'حاجز', 'duration' => 'استخدام واحد', 'stock' => 100]
    ];
    
    // Create contraceptives table
    $pdo->exec("CREATE TABLE IF NOT EXISTS contraceptives (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100),
        duration VARCHAR(100),
        stock INT DEFAULT 0,
        min_stock INT DEFAULT 5,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Insert contraceptives if table is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM contraceptives");
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("INSERT INTO contraceptives (name, type, duration, stock) VALUES (?, ?, ?, ?)");
        foreach ($contraceptives as $contraceptive) {
            $stmt->execute([$contraceptive['name'], $contraceptive['type'], $contraceptive['duration'], $contraceptive['stock']]);
        }
        $response['initialized'][] = 'contraceptives (' . count($contraceptives) . ' items)';
    }
    
    // Initialize vaccines
    $vaccines = [
        ['name' => 'BCG', 'age_months' => 0, 'description' => 'لقاح السل'],
        ['name' => 'التهاب الكبد B', 'age_months' => 0, 'description' => 'الجرعة الأولى'],
        ['name' => 'شلل الأطفال OPV', 'age_months' => 2, 'description' => 'الجرعة الأولى'],
        ['name' => 'الخماسي DPT-HepB-Hib', 'age_months' => 2, 'description' => 'الجرعة الأولى'],
        ['name' => 'المكورات الرئوية', 'age_months' => 2, 'description' => 'الجرعة الأولى'],
        ['name' => 'الروتا', 'age_months' => 2, 'description' => 'الجرعة الأولى'],
        ['name' => 'الحصبة والحصبة الألمانية والنكاف MMR', 'age_months' => 12, 'description' => 'الجرعة الأولى']
    ];
    
    // Create vaccines table
    $pdo->exec("CREATE TABLE IF NOT EXISTS vaccines (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        age_months INT,
        description TEXT,
        stock INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Insert vaccines if table is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM vaccines");
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("INSERT INTO vaccines (name, age_months, description, stock) VALUES (?, ?, ?, ?)");
        foreach ($vaccines as $vaccine) {
            $stmt->execute([$vaccine['name'], $vaccine['age_months'], $vaccine['description'], 50]);
        }
        $response['initialized'][] = 'vaccines (' . count($vaccines) . ' items)';
    }
    
    // Create other necessary tables
    $tables_to_create = [
        "CREATE TABLE IF NOT EXISTS monthly_planning (
            id INT PRIMARY KEY AUTO_INCREMENT,
            month VARCHAR(7) NOT NULL,
            item_type ENUM('medicine', 'contraceptive') NOT NULL,
            item_id INT NOT NULL,
            planned_quantity INT DEFAULT 0,
            actual_quantity INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        "CREATE TABLE IF NOT EXISTS messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            sender_id VARCHAR(50),
            recipient_id VARCHAR(50),
            subject VARCHAR(255),
            content TEXT,
            is_read BOOLEAN DEFAULT FALSE,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        "CREATE TABLE IF NOT EXISTS notifications (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(50),
            title VARCHAR(255),
            message TEXT,
            type VARCHAR(50) DEFAULT 'info',
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )"
    ];
    
    foreach ($tables_to_create as $table_sql) {
        $pdo->exec($table_sql);
    }
    
    $response['initialized'][] = 'additional tables (monthly_planning, messages, notifications)';

    // Create sample center if not exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM centers");
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("INSERT INTO centers (name, location, phone) VALUES
            ('المركز الصحي الرئيسي', 'المدينة المركزية', '123456789'),
            ('مركز صحة الأطفال', 'حي الأطفال', '987654321')");
        $response['initialized'][] = 'sample centers (2 items)';
    }

    // Create sample users if not exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    if ($stmt->fetchColumn() == 0) {
        // Get center ID
        $stmt = $pdo->query("SELECT id FROM centers LIMIT 1");
        $center_id = $stmt->fetchColumn();

        $sample_users = [
            [
                'id' => 'admin001',
                'username' => 'admin',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'name' => 'مدير النظام',
                'center_id' => $center_id,
                'role' => 'admin'
            ],
            [
                'id' => 'nurse001',
                'username' => 'nurse1',
                'password' => password_hash('nurse123', PASSWORD_DEFAULT),
                'name' => 'الممرضة الأولى',
                'center_id' => $center_id,
                'role' => 'nurse'
            ],
            [
                'id' => 'nurse002',
                'username' => 'nurse2',
                'password' => password_hash('nurse123', PASSWORD_DEFAULT),
                'name' => 'الممرضة الثانية',
                'center_id' => $center_id,
                'role' => 'nurse'
            ]
        ];

        $stmt = $pdo->prepare("INSERT INTO users (id, username, password, name, center_id, role) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($sample_users as $user) {
            $stmt->execute([$user['id'], $user['username'], $user['password'], $user['name'], $user['center_id'], $user['role']]);
        }
        $response['initialized'][] = 'sample users (3 items: admin/admin123, nurse1/nurse123, nurse2/nurse123)';
    }

    $response['success'] = true;
    $response['message'] = 'API data initialization completed successfully';
    
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    $response['error'] = true;
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
