<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 اختبار حفظ البيانات عند Refresh</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { 
            border: 2px solid #28a745; 
            margin: 20px 0; 
            padding: 20px; 
            background: white;
            border-radius: 8px;
        }
        .stock-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stock-item h5 { margin: 0 0 10px 0; color: #28a745; }
        .stock-input { 
            width: 80px; padding: 8px; margin: 0 10px;
            border: 2px solid #ced4da; border-radius: 4px; text-align: center;
        }
        .month-name-input { 
            width: 200px; padding: 10px; margin: 10px 0;
            border: 2px solid #ced4da; border-radius: 5px;
        }
        button { 
            padding: 10px 20px; margin: 5px; background: #007bff;
            color: white; border: none; border-radius: 5px; cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button.refresh { background: #dc3545; }
        button.refresh:hover { background: #c82333; }
        .status { 
            padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .grid-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .month-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: #f8f9fa; }
        .refresh-warning {
            background: #fff3cd;
            border: 2px solid #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>🔄 اختبار حفظ البيانات عند Refresh</h1>
    
    <div class="refresh-warning">
        <h3>⚠️ تعليمات الاختبار</h3>
        <p>1. أدخل البيانات في الحقول أدناه</p>
        <p>2. اضغط "حفظ البيانات"</p>
        <p>3. اضغط "محاكاة Refresh" أو اعمل refresh يدوي للصفحة</p>
        <p>4. تحقق من بقاء البيانات</p>
    </div>
    
    <div class="test-section">
        <h2>🎯 أدوات التحكم</h2>
        <button onclick="saveAllData()">💾 حفظ البيانات</button>
        <button onclick="loadAllData()">📂 تحميل البيانات</button>
        <button onclick="clearAllData()">🗑️ مسح البيانات</button>
        <button onclick="addTestData()">📝 إضافة بيانات تجريبية</button>
        <button onclick="simulateRefresh()" class="refresh">🔄 محاكاة Refresh</button>
        <button onclick="window.location.reload()" class="refresh">🔄 Refresh حقيقي</button>
    </div>
    
    <div id="status" class="status info">
        <strong>الحالة:</strong> جاهز للاختبار
    </div>
    
    <div class="grid-container">
        <div>
            <div class="test-section">
                <h3>📝 التخطيط الشهري</h3>
                
                <div class="month-section">
                    <h4>الشهر الأول</h4>
                    <input type="text" class="month-name-input" id="month1Name" 
                           placeholder="اسم الشهر الأول" 
                           onchange="updateMonthName('month1')" 
                           onblur="updateMonthName('month1')">
                    <div id="month1Grid"></div>
                </div>
                
                <div class="month-section">
                    <h4>الشهر الثاني</h4>
                    <input type="text" class="month-name-input" id="month2Name" 
                           placeholder="اسم الشهر الثاني"
                           onchange="updateMonthName('month2')" 
                           onblur="updateMonthName('month2')">
                    <div id="month2Grid"></div>
                </div>
            </div>
        </div>
        
        <div>
            <div class="test-section">
                <h3>📊 حالة البيانات</h3>
                <div id="dataStatus"></div>
                <h4>البيانات المحفوظة:</h4>
                <pre id="dataDisplay">لا توجد بيانات</pre>
            </div>
        </div>
    </div>

    <script>
        let monthlyPlanning = {};
        let currentUser = { id: 'test_user_persistence' };
        
        const vaccineDefinitions = {
            'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B' },
            'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)' },
            'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)' },
            'Penta': { name: 'الخماسي', frenchName: 'Pentavalent' },
            'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)' }
        };
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<strong>الحالة:</strong> ${message}`;
            status.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function initializeData() {
            const defaultVaccines = {};
            Object.keys(vaccineDefinitions).forEach(key => {
                defaultVaccines[key] = 0;
            });
            
            monthlyPlanning = {
                month1: { name: '', vaccines: {...defaultVaccines} },
                month2: { name: '', vaccines: {...defaultVaccines} }
            };
            
            updateDisplay();
            showCurrentData();
            updateStatus('تم تهيئة البيانات', 'info');
        }
        
        function updateDisplay() {
            ['month1', 'month2'].forEach(month => {
                const grid = document.getElementById(`${month}Grid`);
                grid.innerHTML = '';
                
                Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                    const vaccine = vaccineDefinitions[vaccineKey];
                    const currentValue = monthlyPlanning[month].vaccines[vaccineKey] || 0;
                    
                    const vaccineItem = document.createElement('div');
                    vaccineItem.className = 'stock-item';
                    vaccineItem.innerHTML = `
                        <h5>${vaccine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; margin-bottom: 8px;">
                            ${vaccine.frenchName}
                        </div>
                        <div>
                            <input type="number" class="stock-input" id="${month}_${vaccineKey}"
                                   value="${currentValue}" min="0" max="1000"
                                   onchange="updateVaccine('${month}', '${vaccineKey}')"
                                   onblur="updateVaccine('${month}', '${vaccineKey}')">
                            <span>قارورة</span>
                        </div>
                    `;
                    grid.appendChild(vaccineItem);
                });
            });
        }
        
        function updateVaccine(month, vaccineKey) {
            const input = document.getElementById(`${month}_${vaccineKey}`);
            const value = parseInt(input.value) || 0;
            
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].vaccines[vaccineKey] = value;
            
            // حفظ تلقائي
            saveToLocalStorage();
            showCurrentData();
            updateStatus(`تم تحديث ${vaccineKey} في ${month}: ${value}`, 'pass');
        }
        
        function updateMonthName(month) {
            const input = document.getElementById(`${month}Name`);
            const monthName = input.value.trim();
            
            if (!monthlyPlanning[month]) {
                monthlyPlanning[month] = { name: '', vaccines: {} };
            }
            monthlyPlanning[month].name = monthName;
            
            // حفظ تلقائي
            saveToLocalStorage();
            showCurrentData();
            updateStatus(`تم تحديث اسم ${month}: "${monthName}"`, 'pass');
        }
        
        function saveToLocalStorage() {
            try {
                localStorage.setItem(`monthlyPlanning_${currentUser.id}`, JSON.stringify(monthlyPlanning));
                console.log('💾 تم حفظ البيانات في localStorage');
                return true;
            } catch (error) {
                console.error('❌ خطأ في حفظ localStorage:', error);
                updateStatus('خطأ في حفظ البيانات: ' + error.message, 'fail');
                return false;
            }
        }
        
        function loadFromLocalStorage() {
            try {
                const saved = localStorage.getItem(`monthlyPlanning_${currentUser.id}`);
                if (saved) {
                    monthlyPlanning = JSON.parse(saved);
                    
                    // تحديث أسماء الأشهر
                    document.getElementById('month1Name').value = monthlyPlanning.month1?.name || '';
                    document.getElementById('month2Name').value = monthlyPlanning.month2?.name || '';
                    
                    // تحديث العرض
                    updateDisplay();
                    
                    // تحديث قيم الحقول
                    setTimeout(() => {
                        updateInputValues();
                    }, 100);
                    
                    console.log('📂 تم تحميل البيانات من localStorage');
                    return true;
                } else {
                    console.log('⚠️ لا توجد بيانات محفوظة');
                    return false;
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل localStorage:', error);
                updateStatus('خطأ في تحميل البيانات: ' + error.message, 'fail');
                return false;
            }
        }
        
        function updateInputValues() {
            let updatedCount = 0;
            
            ['month1', 'month2'].forEach(month => {
                if (monthlyPlanning[month] && monthlyPlanning[month].vaccines) {
                    Object.keys(monthlyPlanning[month].vaccines).forEach(vaccineKey => {
                        const input = document.getElementById(`${month}_${vaccineKey}`);
                        if (input) {
                            const value = monthlyPlanning[month].vaccines[vaccineKey] || 0;
                            if (input.value != value) {
                                input.value = value;
                                updatedCount++;
                            }
                        }
                    });
                }
            });
            
            console.log(`🔄 تم تحديث ${updatedCount} حقل`);
        }
        
        function saveAllData() {
            const success = saveToLocalStorage();
            if (success) {
                updateStatus('تم حفظ جميع البيانات بنجاح', 'pass');
            } else {
                updateStatus('فشل في حفظ البيانات', 'fail');
            }
            showCurrentData();
        }
        
        function loadAllData() {
            const success = loadFromLocalStorage();
            if (success) {
                updateStatus('تم تحميل البيانات بنجاح', 'pass');
            } else {
                updateStatus('لا توجد بيانات محفوظة', 'warning');
                initializeData();
            }
            showCurrentData();
        }
        
        function clearAllData() {
            localStorage.removeItem(`monthlyPlanning_${currentUser.id}`);
            monthlyPlanning = {};
            document.getElementById('month1Name').value = '';
            document.getElementById('month2Name').value = '';
            
            ['month1Grid', 'month2Grid'].forEach(gridId => {
                document.getElementById(gridId).innerHTML = '';
            });
            
            updateStatus('تم مسح جميع البيانات', 'warning');
            showCurrentData();
        }
        
        function addTestData() {
            // إضافة أسماء الأشهر
            document.getElementById('month1Name').value = 'يناير 2025';
            document.getElementById('month2Name').value = 'فبراير 2025';
            
            updateMonthName('month1');
            updateMonthName('month2');
            
            // إضافة كميات تجريبية
            const testData = {
                month1: { HB1: 50, BCG: 30, VPO: 25 },
                month2: { Penta: 40, RR: 35 }
            };
            
            Object.keys(testData).forEach(month => {
                Object.keys(testData[month]).forEach(vaccine => {
                    const input = document.getElementById(`${month}_${vaccine}`);
                    if (input) {
                        input.value = testData[month][vaccine];
                        updateVaccine(month, vaccine);
                    }
                });
            });
            
            updateStatus('تم إضافة البيانات التجريبية', 'pass');
        }
        
        function simulateRefresh() {
            updateStatus('محاكاة إعادة تحميل الصفحة...', 'info');
            
            // مسح العرض
            ['month1Grid', 'month2Grid'].forEach(gridId => {
                document.getElementById(gridId).innerHTML = '';
            });
            document.getElementById('month1Name').value = '';
            document.getElementById('month2Name').value = '';
            monthlyPlanning = {};
            
            // محاكاة تحميل الصفحة
            setTimeout(() => {
                initializeData();
                const success = loadFromLocalStorage();
                
                if (success) {
                    updateStatus('✅ نجح الاختبار: البيانات محفوظة بعد Refresh', 'pass');
                } else {
                    updateStatus('❌ فشل الاختبار: البيانات ضاعت بعد Refresh', 'fail');
                }
            }, 1000);
        }
        
        function showCurrentData() {
            const display = document.getElementById('dataDisplay');
            display.textContent = JSON.stringify(monthlyPlanning, null, 2);
            
            // عرض إحصائيات
            const statusDiv = document.getElementById('dataStatus');
            const totalVaccines = Object.values(monthlyPlanning).reduce((total, month) => {
                return total + Object.values(month.vaccines || {}).reduce((sum, qty) => sum + qty, 0);
            }, 0);
            
            const monthsWithNames = Object.values(monthlyPlanning).filter(month => month.name && month.name.trim()).length;
            
            statusDiv.innerHTML = `
                <div style="background: #e9ecef; padding: 10px; border-radius: 5px; margin: 10px 0;">
                    <strong>📊 الإحصائيات:</strong><br>
                    🗓️ أشهر بأسماء: ${monthsWithNames}<br>
                    💉 إجمالي القوارير: ${totalVaccines}<br>
                    💾 حجم البيانات: ${JSON.stringify(monthlyPlanning).length} حرف
                </div>
            `;
        }
        
        // تهيئة تلقائية عند تحميل الصفحة
        window.onload = function() {
            updateStatus('تم تحميل الصفحة، جاري فحص البيانات المحفوظة...', 'info');
            
            setTimeout(() => {
                initializeData();
                const hasData = loadFromLocalStorage();
                
                if (hasData) {
                    updateStatus('تم العثور على بيانات محفوظة وتحميلها', 'pass');
                } else {
                    updateStatus('لا توجد بيانات محفوظة، تم تهيئة بيانات جديدة', 'info');
                }
            }, 500);
        };
        
        // حفظ تلقائي كل 5 ثوان
        setInterval(() => {
            if (Object.keys(monthlyPlanning).length > 0) {
                saveToLocalStorage();
            }
        }, 5000);
    </script>
</body>
</html>
