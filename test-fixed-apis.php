<?php
/**
 * Test Fixed APIs
 * اختبار APIs المُصلحة
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'fixed_apis' => [],
    'all_apis_status' => [],
    'success' => false
];

// Test the 2 APIs that were failing
$fixed_apis = [
    'messages-api.php' => 'load',
    'monthly-planning-api.php' => 'test'
];

foreach ($fixed_apis as $api_file => $action) {
    try {
        $test_data = ['action' => $action, 'user_id' => 'test_user'];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/$api_file");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $api_result = json_decode($result, true);
        $status = ($http_code == 200 && $api_result && isset($api_result['success']) && $api_result['success']) ? 'FIXED' : 'STILL_FAILING';
        
        $response['fixed_apis'][$api_file] = [
            'status' => $status,
            'http_code' => $http_code,
            'response' => $api_result,
            'raw_response' => $result
        ];
        
    } catch (Exception $e) {
        $response['fixed_apis'][$api_file] = [
            'status' => 'ERROR',
            'error' => $e->getMessage()
        ];
    }
}

// Test all APIs to get complete status
$all_apis = [
    'children-api.php' => 'load',
    'medicines-api.php' => 'test',
    'vaccines-api.php' => 'load_vaccines',
    'family-planning-api.php' => 'test',
    'user-api-simple.php' => 'test',
    'tasks-api.php' => 'load',
    'messages-api.php' => 'load',
    'monthly-planning-api.php' => 'test',
    'vaccination-status-api.php' => 'test',
    'user-management-api.php' => 'load_all_users'
];

$working_count = 0;
foreach ($all_apis as $api_file => $action) {
    try {
        $test_data = ['action' => $action, 'user_id' => 'test_user'];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://www.csmanager.online/$api_file");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $api_result = json_decode($result, true);
        $is_working = ($http_code == 200 && $api_result && isset($api_result['success']));
        
        if ($is_working) {
            $working_count++;
        }
        
        $response['all_apis_status'][$api_file] = $is_working ? 'WORKING' : 'FAILED';
        
    } catch (Exception $e) {
        $response['all_apis_status'][$api_file] = 'ERROR';
    }
}

$response['summary'] = [
    'total_apis' => count($all_apis),
    'working_apis' => $working_count,
    'success_rate' => round(($working_count / count($all_apis)) * 100, 1) . '%',
    'fixed_apis' => array_keys(array_filter($response['fixed_apis'], function($api) {
        return $api['status'] === 'FIXED';
    }))
];

$response['success'] = $working_count >= 9; // At least 90% working
$response['message'] = $working_count == count($all_apis) ? 
    'All APIs are now working perfectly!' : 
    "$working_count/" . count($all_apis) . " APIs working";

if ($response['success']) {
    $response['next_steps'] = [
        'Clear browser cache and localStorage',
        'Visit https://www.csmanager.online/',
        'Login with admin/password',
        'Test all functionality - everything should save to database'
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
